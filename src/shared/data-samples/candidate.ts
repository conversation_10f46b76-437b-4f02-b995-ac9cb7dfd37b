import type {
  CandidateFormData,
  ICandidateListItemProps,
} from '@shared/types/candidates';

export const tags1: string[] = [
  'established',
  'distracted',
  'readable',
  'content',
  'layout',
  'Lorem Ipsum',
  'more-or-less',
  'normal',
  'distribution',
  'mistaken idea of denouncing pleasure',
  'I will give you a complete account of the system',
  'the master-builder of human happiness',
  'No one rejects',
  'dislikes',
  'or avoids pleasure itself',
  'because it is pleasure',
  'but because those who do not know how to pursue pleasure',
  'rationally encounter consequences',
  'extremely painful',
];
export const candidate1: CandidateFormData = {
  id: '84',
  idType: '',
  createdDate: '2024-12-05T13:57:09.866963',
  lastModifiedDate: '2024-12-05T13:57:09.866964',
  creatorUserId: '***********',
  creatorPageId: '***********',
  ssn: null,
  referralEmiil: '',
  referralPhone: '',
  referralUrl: '',
  fileIds: ['14325'],
  relocation: {
    label: 'no',
    value: 'NO',
  },
  travelRequirement: {
    label: 'no',
    value: 'NO',
  },
  expectedMinimumSalary: 0,
  expectedMaximumSalary: 0,
  expectedMarkup: 0,
  cellNumber: '+***********',
  workNumber: '',
  homeNumber: '',
  skypeId: '',
  linkedinUrl: 'https://linkedin.com/in/qqqw',
  facebookUrl: 'https://facebook.com/wwwe',
  twitterUrl: 'https://x.com/eeeer',
  linkedinUsername: 'qqqw',
  facebookUsername: 'wwwe',
  twitterUsername: 'eeeer',
  otherUrl: '',
  fullAddress: '',
  tags: tags1,
  openToWork: {
    label: 'not_open_to_work',
    value: 'CLOSE',
  },
  profile: {
    id: '84',
    createdDate: '2024-12-05T13:57:09.86697',
    lastModifiedDate: '2024-12-05T13:57:09.86697',
    version: '0',
    originalId: '***********',
    croppedImageUrl:
      'https://storage.googleapis.com/lobox_public_images/image/original/********************************.jpeg',
    bio: 'Results-driven Fullstack Developer with over 10 years of experience specializing in frontend development and the design of No-Code platforms. Proficient in developing scalable web applications, enhancing user experiences, and integrating cloud-based services. Expertise in modern technologies such as React.js, Vue.js, Node.js, and AWS. Proven track record of optimizing system performance, upgrading UI components, and mentoring junior developers. Adept at delivering high quality, user-centric solutions for diverse industries including AI, cryptocurrency, and e-commerce.',
    experiences: [
      {
        id: '470',
        createdDate: '2025-01-05T14:45:02.930684',
        lastModifiedDate: '2025-01-05T14:45:02.930686',
        version: '1',
        originalId: null,
        occupationLookupId: '119807',
        occupationName: 'Salvager',
        employmentType: null,
        workPlaceType: 'ON_SITE',
        companyPageId: '10000015548',
        companyName: 'Academia Advance',
        pageCroppedImageUrl:
          'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1SspanQuMlzFtOVknzwscYIYprW6MYPylKd.png',
        description: null,
        currentlyWorking: true,
        startDate: '2025-01-01T00:00:00',
        endDate: null,
        location: {
          id: '669',
          createdDate: '2025-01-05T14:50:51.280203',
          lastModifiedDate: '2025-01-05T14:50:51.280205',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: 'Los Angeles',
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
        },
      },
      {
        id: '310',
        createdDate: '2024-12-05T13:57:09.867901',
        lastModifiedDate: '2024-12-05T13:57:09.867903',
        version: '0',
        originalId: '1106',
        occupationLookupId: '735',
        occupationName: 'Accommodation Management Officer',
        employmentType: 'SELF_EMPLOYED',
        workPlaceType: 'HYBRID',
        companyPageId: '10000014638',
        companyName: 'Armament Arts Defense',
        pageCroppedImageUrl:
          'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1IifFZfLvxY3RujFbGZ6zrjumcQT2aPX7.png',
        description: null,
        currentlyWorking: true,
        startDate: '2024-05-01T00:00:00',
        endDate: null,
        location: {
          id: '453',
          createdDate: '2024-12-05T13:57:09.867962',
          lastModifiedDate: '2024-12-05T13:57:09.867962',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: null,
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
        },
      },
      {
        id: '311',
        createdDate: '2024-12-05T13:57:09.867975',
        lastModifiedDate: '2024-12-05T13:57:09.867976',
        version: '0',
        originalId: '1349',
        occupationLookupId: '686',
        occupationName: 'Accessibility and Accommodations Expert',
        employmentType: 'FULL_TIME',
        workPlaceType: 'ON_SITE',
        companyPageId: '10000014638',
        companyName: 'Armament Arts Defense',
        pageCroppedImageUrl:
          'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1IifFZfLvxY3RujFbGZ6zrjumcQT2aPX7.png',
        description: '<p>some description here</p>',
        currentlyWorking: null,
        startDate: '2024-01-01T00:00:00',
        endDate: '2024-08-01T00:00:00',
        location: {
          id: '454',
          createdDate: '2024-12-05T13:57:09.867977',
          lastModifiedDate: '2024-12-05T13:57:09.867977',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: null,
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
        },
      },
      {
        id: '312',
        createdDate: '2024-12-05T13:57:09.86797',
        lastModifiedDate: '2024-12-05T13:57:09.86797',
        version: '1',
        originalId: '16465',
        occupationLookupId: '14306',
        occupationName: 'Breakbeat Remixer',
        employmentType: 'SELF_EMPLOYED',
        workPlaceType: 'REMOTE',
        companyPageId: '10000016982',
        companyName: 'Adweek',
        pageCroppedImageUrl:
          'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1m_QCvxQpUp1SXugAzkVK7V2qXq1eZ281.webp',
        description: '<p>some text can jump hee</p>',
        currentlyWorking: true,
        startDate: '2017-02-01T00:00:00',
        endDate: null,
        location: {
          id: '465',
          createdDate: '2024-12-09T16:13:49.42455',
          lastModifiedDate: '2024-12-09T16:13:49.424553',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: null,
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
        },
      },
    ],
    skills: [
      {
        id: '2979',
        createdDate: '2024-12-05T13:57:09.869287',
        lastModifiedDate: '2024-12-05T13:57:09.869287',
        version: '0',
        originalId: '1561',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'UI/UX.',
      },
      {
        id: '2973',
        createdDate: '2024-12-05T13:57:09.869218',
        lastModifiedDate: '2024-12-05T13:57:09.869218',
        version: '0',
        originalId: '1508',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'AI,',
      },
      {
        id: '2984',
        createdDate: '2024-12-05T13:57:09.869251',
        lastModifiedDate: '2024-12-05T13:57:09.869251',
        version: '0',
        originalId: '1530',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'AI',
      },
      {
        id: '3030',
        createdDate: '2024-12-05T13:57:09.869263',
        lastModifiedDate: '2024-12-05T13:57:09.869263',
        version: '0',
        originalId: '1541',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Customer Success Management',
      },
      {
        id: '2997',
        createdDate: '2024-12-05T13:57:09.869254',
        lastModifiedDate: '2024-12-05T13:57:09.869254',
        version: '0',
        originalId: '1532',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'mentoring',
      },
      {
        id: '3015',
        createdDate: '2024-12-05T13:57:09.869336',
        lastModifiedDate: '2024-12-05T13:57:09.86934',
        version: '0',
        originalId: '1591',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'REST APIs',
      },
      {
        id: '3026',
        createdDate: '2024-12-05T13:57:09.869289',
        lastModifiedDate: '2024-12-05T13:57:09.869289',
        version: '0',
        originalId: '1563',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'data visualization',
      },
      {
        id: '3002',
        createdDate: '2024-12-05T13:57:09.869325',
        lastModifiedDate: '2024-12-05T13:57:09.869326',
        version: '0',
        originalId: '1587',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Hook Forms,',
      },
      {
        id: '2995',
        createdDate: '2024-12-05T13:57:09.869274',
        lastModifiedDate: '2024-12-05T13:57:09.869274',
        version: '0',
        originalId: '1550',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'integrating',
      },
      {
        id: '3012',
        createdDate: '2024-12-05T13:57:09.86923',
        lastModifiedDate: '2024-12-05T13:57:09.869231',
        version: '0',
        originalId: '1514',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Webpack,',
      },
      {
        id: '3040',
        createdDate: '2024-12-05T13:57:09.869284',
        lastModifiedDate: '2024-12-05T13:57:09.869284',
        version: '0',
        originalId: '1558',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'AgGrid.',
      },
      {
        id: '2977',
        createdDate: '2024-12-05T13:57:09.869217',
        lastModifiedDate: '2024-12-05T13:57:09.869217',
        version: '0',
        originalId: '1507',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'text analysis',
      },
      {
        id: '3039',
        createdDate: '2024-12-05T13:57:09.869292',
        lastModifiedDate: '2024-12-05T13:57:09.869293',
        version: '0',
        originalId: '1566',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Agriculture CRM',
      },
      {
        id: '2961',
        createdDate: '2024-12-05T13:57:09.869237',
        lastModifiedDate: '2024-12-05T13:57:09.869238',
        version: '0',
        originalId: '1519',
        type: null,
        level: 'INTERMEDIATE',
        skillLookupId: null,
        name: 'UI',
      },
      {
        id: '2996',
        createdDate: '2024-12-05T13:57:09.869334',
        lastModifiedDate: '2024-12-05T13:57:09.869335',
        version: '0',
        originalId: '1590',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'developing scalable web applications,',
      },
      {
        id: '3037',
        createdDate: '2024-12-05T13:57:09.869281',
        lastModifiedDate: '2024-12-05T13:57:09.869281',
        version: '0',
        originalId: '1556',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Enterprise Management System',
      },
      {
        id: '3017',
        createdDate: '2024-12-05T13:57:09.869304',
        lastModifiedDate: '2024-12-05T13:57:09.869305',
        version: '0',
        originalId: '1576',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'soft delete',
      },
      {
        id: '2965',
        createdDate: '2024-12-05T13:57:09.869241',
        lastModifiedDate: '2024-12-05T13:57:09.869241',
        version: '0',
        originalId: '1522',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'React.js',
      },
      {
        id: '2986',
        createdDate: '2024-12-05T13:57:09.869203',
        lastModifiedDate: '2024-12-05T13:57:09.869203',
        version: '0',
        originalId: '1506',
        type: null,
        level: 'INTERMEDIATE',
        skillLookupId: null,
        name: 'Food delivery',
      },
      {
        id: '3006',
        createdDate: '2024-12-05T13:57:09.869243',
        lastModifiedDate: '2024-12-05T13:57:09.869243',
        version: '0',
        originalId: '1524',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'frontend development',
      },
      {
        id: '2998',
        createdDate: '2024-12-05T13:57:09.869253',
        lastModifiedDate: '2024-12-05T13:57:09.869253',
        version: '0',
        originalId: '1531',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'system performance,',
      },
      {
        id: '3027',
        createdDate: '2024-12-05T13:57:09.869255',
        lastModifiedDate: '2024-12-05T13:57:09.869255',
        version: '0',
        originalId: '1533',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Fullstack',
      },
      {
        id: '3038',
        createdDate: '2024-12-05T13:57:09.8693',
        lastModifiedDate: '2024-12-05T13:57:09.8693',
        version: '0',
        originalId: '1573',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Inventory management System.',
      },
      {
        id: '3005',
        createdDate: '2024-12-05T13:57:09.869245',
        lastModifiedDate: '2024-12-05T13:57:09.869245',
        version: '0',
        originalId: '1526',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: '(Frontend',
      },
      {
        id: '3033',
        createdDate: '2024-12-05T13:57:09.869307',
        lastModifiedDate: '2024-12-05T13:57:09.869308',
        version: '0',
        originalId: '1578',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'SNS.',
      },
      {
        id: '2969',
        createdDate: '2024-12-05T13:57:09.869306',
        lastModifiedDate: '2024-12-05T13:57:09.869306',
        version: '0',
        originalId: '1577',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'AWS.',
      },
      {
        id: '3021',
        createdDate: '2024-12-05T13:57:09.869273',
        lastModifiedDate: '2024-12-05T13:57:09.869273',
        version: '0',
        originalId: '1549',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Generator',
      },
      {
        id: '2964',
        createdDate: '2024-12-05T13:57:09.869244',
        lastModifiedDate: '2024-12-05T13:57:09.869244',
        version: '0',
        originalId: '1525',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Google Maps API.',
      },
      {
        id: '2992',
        createdDate: '2024-12-05T13:57:09.869233',
        lastModifiedDate: '2024-12-05T13:57:09.869233',
        version: '0',
        originalId: '1516',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'E-commerce online shop',
      },
      {
        id: '3019',
        createdDate: '2024-12-05T13:57:09.869301',
        lastModifiedDate: '2024-12-05T13:57:09.869301',
        version: '0',
        originalId: '1574',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Smart',
      },
      {
        id: '2976',
        createdDate: '2024-12-05T13:57:09.869246',
        lastModifiedDate: '2024-12-05T13:57:09.869247',
        version: '0',
        originalId: '1527',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'UX',
      },
      {
        id: '3003',
        createdDate: '2024-12-05T13:57:09.869229',
        lastModifiedDate: '2024-12-05T13:57:09.869229',
        version: '0',
        originalId: '1513',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'QA',
      },
      {
        id: '3022',
        createdDate: '2024-12-05T13:57:09.869322',
        lastModifiedDate: '2024-12-05T13:57:09.869322',
        version: '0',
        originalId: '1586',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'user-centric',
      },
      {
        id: '2975',
        createdDate: '2024-12-05T13:57:09.869309',
        lastModifiedDate: '2024-12-05T13:57:09.869309',
        version: '0',
        originalId: '1579',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'AWS,',
      },
      {
        id: '3011',
        createdDate: '2024-12-05T13:57:09.869268',
        lastModifiedDate: '2024-12-05T13:57:09.869268',
        version: '0',
        originalId: '1545',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'CMS.',
      },
      {
        id: '2971',
        createdDate: '2024-12-05T13:57:09.86924',
        lastModifiedDate: '2024-12-05T13:57:09.86924',
        version: '0',
        originalId: '1521',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'optimizing',
      },
      {
        id: '2967',
        createdDate: '2024-12-05T13:57:09.869286',
        lastModifiedDate: '2024-12-05T13:57:09.869286',
        version: '0',
        originalId: '1560',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Docker Swarm,',
      },
      {
        id: '3042',
        createdDate: '2024-12-05T13:57:09.869234',
        lastModifiedDate: '2024-12-05T13:57:09.869235',
        version: '0',
        originalId: '1517',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'TypeScript,',
      },
      {
        id: '2962',
        createdDate: '2024-12-05T13:57:09.869259',
        lastModifiedDate: '2024-12-05T13:57:09.869259',
        version: '0',
        originalId: '1537',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Vue.js',
      },
      {
        id: '3046',
        createdDate: '2024-12-05T13:57:09.869258',
        lastModifiedDate: '2024-12-05T13:57:09.869258',
        version: '0',
        originalId: '1536',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Python,',
      },
      {
        id: '2982',
        createdDate: '2024-12-05T13:57:09.869294',
        lastModifiedDate: '2024-12-05T13:57:09.869295',
        version: '0',
        originalId: '1568',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Vue.js,',
      },
      {
        id: '3014',
        createdDate: '2024-12-05T13:57:09.86929',
        lastModifiedDate: '2024-12-05T13:57:09.86929',
        version: '0',
        originalId: '1564',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Google Cloud Run,',
      },
      {
        id: '3028',
        createdDate: '2024-12-05T13:57:09.869313',
        lastModifiedDate: '2024-12-05T13:57:09.869313',
        version: '0',
        originalId: '1581',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'AWS SES',
      },
      {
        id: '2974',
        createdDate: '2024-12-05T13:57:09.869275',
        lastModifiedDate: '2024-12-05T13:57:09.869275',
        version: '0',
        originalId: '1551',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'e-commerce.',
      },
      {
        id: '2985',
        createdDate: '2024-12-05T13:57:09.869288',
        lastModifiedDate: '2024-12-05T13:57:09.869288',
        version: '0',
        originalId: '1562',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Bootstrap',
      },
      {
        id: '3007',
        createdDate: '2024-12-05T13:57:09.869298',
        lastModifiedDate: '2024-12-05T13:57:09.869298',
        version: '0',
        originalId: '1571',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Single Page Applications (SPA),',
      },
      {
        id: '2981',
        createdDate: '2024-12-05T13:57:09.869277',
        lastModifiedDate: '2024-12-05T13:57:09.869277',
        version: '0',
        originalId: '1553',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Fullstack Developer',
      },
      {
        id: '3008',
        createdDate: '2024-12-05T13:57:09.869242',
        lastModifiedDate: '2024-12-05T13:57:09.869242',
        version: '0',
        originalId: '1523',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'user dashboard',
      },
      {
        id: '3023',
        createdDate: '2024-12-05T13:57:09.869332',
        lastModifiedDate: '2024-12-05T13:57:09.869332',
        version: '0',
        originalId: '1589',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'frontend',
      },
      {
        id: '3036',
        createdDate: '2024-12-05T13:57:09.869236',
        lastModifiedDate: '2024-12-05T13:57:09.869236',
        version: '0',
        originalId: '1518',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'React.js,',
      },
      {
        id: '2990',
        createdDate: '2024-12-05T13:57:09.869276',
        lastModifiedDate: '2024-12-05T13:57:09.869276',
        version: '0',
        originalId: '1552',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Terraform,',
      },
      {
        id: '2994',
        createdDate: '2024-12-05T13:57:09.869261',
        lastModifiedDate: '2024-12-05T13:57:09.869261',
        version: '0',
        originalId: '1539',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Static Site Generation (SSG),',
      },
      {
        id: '2987',
        createdDate: '2024-12-05T13:57:09.86926',
        lastModifiedDate: '2024-12-05T13:57:09.86926',
        version: '0',
        originalId: '1538',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Results-driven',
      },
      {
        id: '3031',
        createdDate: '2024-12-05T13:57:09.869222',
        lastModifiedDate: '2024-12-05T13:57:09.869222',
        version: '0',
        originalId: '1511',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Styled-Components,',
      },
      {
        id: '3013',
        createdDate: '2024-12-05T13:57:09.869262',
        lastModifiedDate: '2024-12-05T13:57:09.869262',
        version: '0',
        originalId: '1540',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'CloudWatch',
      },
      {
        id: '3025',
        createdDate: '2024-12-05T13:57:09.869303',
        lastModifiedDate: '2024-12-05T13:57:09.869303',
        version: '0',
        originalId: '1575',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Stripe.',
      },
      {
        id: '2978',
        createdDate: '2024-12-05T13:57:09.869285',
        lastModifiedDate: '2024-12-05T13:57:09.869285',
        version: '0',
        originalId: '1559',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Storybook',
      },
      {
        id: '3004',
        createdDate: '2024-12-05T13:57:09.869328',
        lastModifiedDate: '2024-12-05T13:57:09.869329',
        version: '0',
        originalId: '1588',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'HTML,',
      },
      {
        id: '3000',
        createdDate: '2024-12-05T13:57:09.869299',
        lastModifiedDate: '2024-12-05T13:57:09.869299',
        version: '0',
        originalId: '1572',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'machine learning',
      },
      {
        id: '3010',
        createdDate: '2024-12-05T13:57:09.869318',
        lastModifiedDate: '2024-12-05T13:57:09.869318',
        version: '0',
        originalId: '1584',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'microservices architecture',
      },
      {
        id: '3016',
        createdDate: '2024-12-05T13:57:09.86922',
        lastModifiedDate: '2024-12-05T13:57:09.86922',
        version: '0',
        originalId: '1509',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'PostgreSQL',
      },
      {
        id: '2989',
        createdDate: '2024-12-05T13:57:09.869291',
        lastModifiedDate: '2024-12-05T13:57:09.869291',
        version: '0',
        originalId: '1565',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Figma.',
      },
      {
        id: '2968',
        createdDate: '2024-12-05T13:57:09.869269',
        lastModifiedDate: '2024-12-05T13:57:09.869269',
        version: '0',
        originalId: '1546',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: '(GCP).',
      },
      {
        id: '3035',
        createdDate: '2024-12-05T13:57:09.869256',
        lastModifiedDate: '2024-12-05T13:57:09.869256',
        version: '0',
        originalId: '1534',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'messaging',
      },
      {
        id: '3043',
        createdDate: '2024-12-05T13:57:09.869266',
        lastModifiedDate: '2024-12-05T13:57:09.869266',
        version: '0',
        originalId: '1543',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'CSS,',
      },
      {
        id: '3029',
        createdDate: '2024-12-05T13:57:09.869314',
        lastModifiedDate: '2024-12-05T13:57:09.869315',
        version: '0',
        originalId: '1582',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Redux,',
      },
      {
        id: '3009',
        createdDate: '2024-12-05T13:57:09.86927',
        lastModifiedDate: '2024-12-05T13:57:09.86927',
        version: '0',
        originalId: '1547',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'data flow',
      },
      {
        id: '2988',
        createdDate: '2024-12-05T13:57:09.869257',
        lastModifiedDate: '2024-12-05T13:57:09.869257',
        version: '0',
        originalId: '1535',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'cloud-based services.',
      },
      {
        id: '2980',
        createdDate: '2024-12-05T13:57:09.869264',
        lastModifiedDate: '2024-12-05T13:57:09.869265',
        version: '0',
        originalId: '1542',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Real Estate',
      },
      {
        id: '2999',
        createdDate: '2024-12-05T13:57:09.86928',
        lastModifiedDate: '2024-12-05T13:57:09.86928',
        version: '0',
        originalId: '1555',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Cryptocurrency exchange',
      },
      {
        id: '2963',
        createdDate: '2024-12-05T13:57:09.869249',
        lastModifiedDate: '2024-12-05T13:57:09.86925',
        version: '0',
        originalId: '1529',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'APIs',
      },
      {
        id: '2983',
        createdDate: '2024-12-05T13:57:09.869239',
        lastModifiedDate: '2024-12-05T13:57:09.869239',
        version: '0',
        originalId: '1520',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'real-time price charts.',
      },
      {
        id: '2993',
        createdDate: '2024-12-05T13:57:09.869267',
        lastModifiedDate: '2024-12-05T13:57:09.869267',
        version: '0',
        originalId: '1544',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'MongoDB,',
      },
      {
        id: '3034',
        createdDate: '2024-12-05T13:57:09.869311',
        lastModifiedDate: '2024-12-05T13:57:09.869311',
        version: '0',
        originalId: '1580',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'data filters',
      },
      {
        id: '3020',
        createdDate: '2024-12-05T13:57:09.869319',
        lastModifiedDate: '2024-12-05T13:57:09.86932',
        version: '0',
        originalId: '1585',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'codebase',
      },
      {
        id: '3032',
        createdDate: '2024-12-05T13:57:09.869228',
        lastModifiedDate: '2024-12-05T13:57:09.869228',
        version: '0',
        originalId: '1512',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Node.js,',
      },
      {
        id: '3045',
        createdDate: '2024-12-05T13:57:09.869297',
        lastModifiedDate: '2024-12-05T13:57:09.869297',
        version: '0',
        originalId: '1570',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'React Router Dom (RRD),',
      },
      {
        id: '2991',
        createdDate: '2024-12-05T13:57:09.869231',
        lastModifiedDate: '2024-12-05T13:57:09.869232',
        version: '0',
        originalId: '1515',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Apollo GraphQL caching',
      },
      {
        id: '2972',
        createdDate: '2024-12-05T13:57:09.869278',
        lastModifiedDate: '2024-12-05T13:57:09.869278',
        version: '0',
        originalId: '1554',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'No-Code Platform Design',
      },
      {
        id: '3018',
        createdDate: '2024-12-05T13:57:09.869316',
        lastModifiedDate: '2024-12-05T13:57:09.869316',
        version: '0',
        originalId: '1583',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'cryptocurrency,',
      },
      {
        id: '3001',
        createdDate: '2024-12-05T13:57:09.869296',
        lastModifiedDate: '2024-12-05T13:57:09.869296',
        version: '0',
        originalId: '1569',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'No-Code platforms.',
      },
      {
        id: '2970',
        createdDate: '2024-12-05T13:57:09.869248',
        lastModifiedDate: '2024-12-05T13:57:09.869248',
        version: '0',
        originalId: '1528',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'Server-Side Rendering (SSR),',
      },
      {
        id: '2966',
        createdDate: '2024-12-05T13:57:09.869282',
        lastModifiedDate: '2024-12-05T13:57:09.869282',
        version: '0',
        originalId: '1557',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'computer science',
      },
      {
        id: '3024',
        createdDate: '2024-12-05T13:57:09.869271',
        lastModifiedDate: '2024-12-05T13:57:09.869272',
        version: '0',
        originalId: '1548',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'MySQL,',
      },
      {
        id: '3041',
        createdDate: '2024-12-05T13:57:09.869293',
        lastModifiedDate: '2024-12-05T13:57:09.869294',
        version: '0',
        originalId: '1567',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'live',
      },
      {
        id: '3044',
        createdDate: '2024-12-05T13:57:09.869221',
        lastModifiedDate: '2024-12-05T13:57:09.869221',
        version: '0',
        originalId: '1510',
        type: null,
        level: 'BEGINNER',
        skillLookupId: null,
        name: 'MongoDB Atlas.',
      },
    ],
    languages: [
      {
        id: '57',
        createdDate: '2024-12-05T13:57:09.868672',
        lastModifiedDate: '2024-12-05T13:57:09.868673',
        version: '0',
        originalId: '173',
        languageLookupId: null,
        name: 'Persian',
        level: null,
        countryFlagUrl: null,
      },
      {
        id: '56',
        createdDate: '2024-12-05T13:57:09.868724',
        lastModifiedDate: '2024-12-05T13:57:09.868724',
        version: '0',
        originalId: '174',
        languageLookupId: null,
        name: 'English',
        level: null,
        countryFlagUrl: null,
      },
    ],
    educations: [
      {
        id: '59',
        createdDate: '2025-01-05T14:45:43.760638',
        lastModifiedDate: '2025-01-05T14:45:43.760639',
        version: '1',
        originalId: null,
        schoolPageId: '10000015887',
        schoolName: 'AeroTech Aerospace',
        pageCroppedImageUrl:
          'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1C8VevPEbGAR02sGgjHrpewbyJlS0ks7R.png',
        majorLookupId: '3',
        majorName: 'Addictions',
        degree: 'DIPLOMA',
        startDate: '2025-01-01T00:00:00',
        endDate: null,
        description: null,
        currentlyStudying: true,
        location: {
          id: '668',
          createdDate: '2025-01-05T14:49:57.136399',
          lastModifiedDate: '2025-01-05T14:49:57.136401',
          version: '0',
          externalId: 'here:cm:namedplace:22928282',
          title: 'Afghanistan',
          lat: '34.53313',
          lon: '69.10221',
          cityName: 'Afghanistan',
          cityCode: 'afghanistan_afg',
          countryCode: 'AFG',
          category: 'city-town-village',
          fullAddress: null,
        },
      },
      {
        id: '60',
        createdDate: '2025-01-05T14:47:48.693635',
        lastModifiedDate: '2025-01-05T14:47:48.693636',
        version: '0',
        originalId: null,
        schoolPageId: '10000015887',
        schoolName: 'AeroTech Aerospace',
        pageCroppedImageUrl:
          'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1C8VevPEbGAR02sGgjHrpewbyJlS0ks7R.png',
        majorLookupId: '170',
        majorName: 'Gerontology and Aging',
        degree: 'MIDDLE_SCHOOL',
        startDate: '2025-01-01T00:00:00',
        endDate: null,
        description: null,
        currentlyStudying: true,
        location: {
          id: '667',
          createdDate: '2025-01-05T14:47:48.693652',
          lastModifiedDate: '2025-01-05T14:47:48.693653',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: 'Los Angeles',
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
        },
      },
      {
        id: '58',
        createdDate: '2025-01-05T14:45:15.525167',
        lastModifiedDate: '2025-01-05T14:45:15.525168',
        version: '0',
        originalId: null,
        schoolPageId: '***********',
        schoolName: 'AccountWise',
        pageCroppedImageUrl:
          'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1Yb7FHjNwnhqadjH2R2mUc-_a84BFuvtB.png',
        majorLookupId: '170',
        majorName: 'Gerontology and Aging',
        degree: 'MIDDLE_SCHOOL',
        startDate: '2025-01-01T00:00:00',
        endDate: null,
        description: null,
        currentlyStudying: true,
        location: {
          id: '665',
          createdDate: '2025-01-05T14:45:15.525238',
          lastModifiedDate: '2025-01-05T14:45:15.52524',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: 'Los Angeles',
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
        },
      },
    ],
    type: 'PERSON',
    username: 'john_dow',
    email: {
      value: '<EMAIL>',
    },
    occupation: {
      label: 'Dependency Defense Advocate',
      value: '34928',
    },
    birthDate: {
      value: null,
    },
    phone: {
      value: '',
    },
    location: {
      id: '452',
      createdDate: '2024-12-05T13:57:09.869404',
      lastModifiedDate: '2024-12-05T13:57:09.869404',
      version: '0',
      externalId: 'here:cm:namedplace:20653299',
      title: 'Anatoliki Makedonia Thraki, Greece',
      lat: '41.11868',
      lon: '25.40377',
      cityName: null,
      cityCode: null,
      countryCode: 'GRC',
      category: 'administrative-region',
      fullAddress: null,
      label: 'Anatoliki Makedonia Thraki, Greece',
      value: 'here:cm:namedplace:20653299',
    },
    fullName: 'John Dow',
    usernameAtSign: '@john_dow',
    userLink: '/john_dow',
    surname: 'Dow',
    name: 'John',
  },
  _experiences: [
    {
      id: '470',
      companyPageId: '10000015548',
      image:
        'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1SspanQuMlzFtOVknzwscYIYprW6MYPylKd.png',
      firstText: 'Salvager',
      secondText: 'Jan 2025 - Present',
      secondTextHelper: '5 days',
      fourthText: 'Academia Advance',
      fourthTextDotHelper: ['Los Angeles, CA, United States'],
      fourthTextAdditionalProps: {
        objectId: '10000015548',
        isPageAnonymous: false,
      },
      longText: null,
      realData: {
        location: {
          id: '669',
          createdDate: '2025-01-05T14:50:51.280203',
          lastModifiedDate: '2025-01-05T14:50:51.280205',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: 'Los Angeles',
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
          label: 'Los Angeles, CA, United States',
          value: 'here:cm:namedplace:********',
        },
        id: '470',
        job: {
          label: 'Salvager',
          value: '119807',
          public: false,
        },
        company: {
          label: 'Academia Advance',
          value: '10000015548',
          public: false,
          image:
            'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1SspanQuMlzFtOVknzwscYIYprW6MYPylKd.png',
        },
        employmentType: {
          value: null,
        },
        workPlaceType: {
          label: 'ON_SITE',
          value: 'ON_SITE',
          tooltip: 'employ_work_o_s',
          icon: 'building',
        },
        description: null,
        originalId: null,
        currentlyWorking: true,
        startDate: '2025-01-01T00:00:00',
        endDate: null,
      },
      durationObj: {
        years: 0,
        months: 0,
        days: 5,
        hours: 11,
        minutes: 3,
        seconds: 54,
      },
      objectId: '10000015548',
    },
    {
      id: '10000014638',
      companyPageId: '10000014638',
      image:
        'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1IifFZfLvxY3RujFbGZ6zrjumcQT2aPX7.png',
      firstText: 'Armament Arts Defense',
      firstTextAdditionalProps: {
        objectId: '10000014638',
        isPageAnonymous: false,
      },
      objectId: '10000014638',
      steps: [
        {
          id: '310',
          firstText: 'Accommodation Management Officer',
          firstTextDotHelper: ['Los Angeles, CA, United States'],
          secondText: 'May 2024 - Present',
          secondTextHelper: '8 mos 5 days',
          secondTextSecondHelper: 'SELF_EMPLOYED',
          longText: null,
          realData: {
            location: {
              id: '453',
              createdDate: '2024-12-05T13:57:09.867962',
              lastModifiedDate: '2024-12-05T13:57:09.867962',
              version: '0',
              externalId: 'here:cm:namedplace:********',
              title: 'Los Angeles, CA, United States',
              lat: '34.05357',
              lon: '-118.24545',
              cityName: null,
              cityCode: 'los_angeles_usa_california',
              countryCode: 'USA',
              category: 'city-town-village',
              fullAddress: null,
              label: 'Los Angeles, CA, United States',
              value: 'here:cm:namedplace:********',
            },
            id: '310',
            job: {
              label: 'Accommodation Management Officer',
              value: '735',
              public: false,
            },
            company: {
              label: 'Armament Arts Defense',
              value: '10000014638',
              public: false,
              image:
                'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1IifFZfLvxY3RujFbGZ6zrjumcQT2aPX7.png',
            },
            employmentType: {
              value: 'SELF_EMPLOYED',
              label: 'SELF_EMPLOYED',
            },
            workPlaceType: {
              label: 'HYBRID',
              value: 'HYBRID',
              tooltip: 'employ_work_o_s_off',
              icon: 'house-person-leave',
            },
            description: null,
            originalId: '1106',
            currentlyWorking: true,
            startDate: '2024-05-01T00:00:00',
            endDate: null,
          },
          durationObj: {
            years: 0,
            months: 8,
            days: 5,
            hours: 11,
            minutes: 3,
            seconds: 54,
          },
        },
        {
          id: '311',
          firstText: 'Accessibility and Accommodations Expert',
          firstTextDotHelper: ['Los Angeles, CA, United States'],
          secondText: 'Jan 2024 - Aug 2024',
          secondTextHelper: '7 mos',
          secondTextSecondHelper: 'FULL_TIME',
          longText: '<p>some description here</p>',
          realData: {
            location: {
              id: '454',
              createdDate: '2024-12-05T13:57:09.867977',
              lastModifiedDate: '2024-12-05T13:57:09.867977',
              version: '0',
              externalId: 'here:cm:namedplace:********',
              title: 'Los Angeles, CA, United States',
              lat: '34.05357',
              lon: '-118.24545',
              cityName: null,
              cityCode: 'los_angeles_usa_california',
              countryCode: 'USA',
              category: 'city-town-village',
              fullAddress: null,
              label: 'Los Angeles, CA, United States',
              value: 'here:cm:namedplace:********',
            },
            id: '311',
            job: {
              label: 'Accessibility and Accommodations Expert',
              value: '686',
              public: false,
            },
            company: {
              label: 'Armament Arts Defense',
              value: '10000014638',
              public: false,
              image:
                'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1IifFZfLvxY3RujFbGZ6zrjumcQT2aPX7.png',
            },
            employmentType: {
              value: 'FULL_TIME',
              label: 'FULL_TIME',
            },
            workPlaceType: {
              label: 'ON_SITE',
              value: 'ON_SITE',
              tooltip: 'employ_work_o_s',
              icon: 'building',
            },
            description: '<p>some description here</p>',
            originalId: '1349',
            currentlyWorking: null,
            startDate: '2024-01-01T00:00:00',
            endDate: '2024-08-01T00:00:00',
          },
          durationObj: {
            years: 0,
            months: 7,
            days: 0,
            hours: 0,
            minutes: 0,
            seconds: 0,
          },
        },
      ],
    },
    {
      id: '312',
      companyPageId: '10000016982',
      image:
        'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1m_QCvxQpUp1SXugAzkVK7V2qXq1eZ281.webp',
      firstText: 'Breakbeat Remixer',
      secondText: 'Feb 2017 - Present',
      secondTextHelper: '7 yrs 11 mos',
      secondTextSecondHelper: 'SELF_EMPLOYED',
      fourthText: 'Adweek',
      fourthTextDotHelper: ['Los Angeles, CA, United States'],
      fourthTextAdditionalProps: {
        objectId: '10000016982',
        isPageAnonymous: false,
      },
      longText: '<p>some text can jump hee</p>',
      realData: {
        location: {
          id: '465',
          createdDate: '2024-12-09T16:13:49.42455',
          lastModifiedDate: '2024-12-09T16:13:49.424553',
          version: '0',
          externalId: 'here:cm:namedplace:********',
          title: 'Los Angeles, CA, United States',
          lat: '34.05357',
          lon: '-118.24545',
          cityName: null,
          cityCode: 'los_angeles_usa_california',
          countryCode: 'USA',
          category: 'city-town-village',
          fullAddress: null,
          label: 'Los Angeles, CA, United States',
          value: 'here:cm:namedplace:********',
        },
        id: '312',
        job: {
          label: 'Breakbeat Remixer',
          value: '14306',
          public: false,
        },
        company: {
          label: 'Adweek',
          value: '10000016982',
          public: false,
          image:
            'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1m_QCvxQpUp1SXugAzkVK7V2qXq1eZ281.webp',
        },
        employmentType: {
          value: 'SELF_EMPLOYED',
          label: 'SELF_EMPLOYED',
        },
        workPlaceType: {
          label: 'REMOTE',
          value: 'REMOTE',
          tooltip: 'employ_work_off',
          icon: 'house-light',
        },
        description: '<p>some text can jump hee</p>',
        originalId: '16465',
        currentlyWorking: true,
        startDate: '2017-02-01T00:00:00',
        endDate: null,
      },
      durationObj: {
        years: 7,
        months: 11,
        days: 5,
        hours: 11,
        minutes: 3,
        seconds: 54,
      },
      objectId: '10000016982',
    },
  ],
  _educations: [
    {
      id: '59',
      createdDate: '2025-01-05T14:45:43.760638',
      lastModifiedDate: '2025-01-05T14:45:43.760639',
      version: '1',
      originalId: null,
      schoolPageId: '10000015887',
      schoolName: 'AeroTech Aerospace',
      pageCroppedImageUrl:
        'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1C8VevPEbGAR02sGgjHrpewbyJlS0ks7R.png',
      majorLookupId: '3',
      majorName: 'Addictions',
      degree: 'DIPLOMA',
      startDate: '2025-01-01T00:00:00',
      endDate: null,
      description: null,
      currentlyStudying: true,
      location: {
        id: '668',
        createdDate: '2025-01-05T14:49:57.136399',
        lastModifiedDate: '2025-01-05T14:49:57.136401',
        version: '0',
        externalId: 'here:cm:namedplace:22928282',
        title: 'Afghanistan',
        lat: '34.53313',
        lon: '69.10221',
        cityName: 'Afghanistan',
        cityCode: 'afghanistan_afg',
        countryCode: 'AFG',
        category: 'city-town-village',
        fullAddress: null,
      },
    },
    {
      id: '60',
      createdDate: '2025-01-05T14:47:48.693635',
      lastModifiedDate: '2025-01-05T14:47:48.693636',
      version: '0',
      originalId: null,
      schoolPageId: '10000015887',
      schoolName: 'AeroTech Aerospace',
      pageCroppedImageUrl:
        'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1C8VevPEbGAR02sGgjHrpewbyJlS0ks7R.png',
      majorLookupId: '170',
      majorName: 'Gerontology and Aging',
      degree: 'MIDDLE_SCHOOL',
      startDate: '2025-01-01T00:00:00',
      endDate: null,
      description: null,
      currentlyStudying: true,
      location: {
        id: '667',
        createdDate: '2025-01-05T14:47:48.693652',
        lastModifiedDate: '2025-01-05T14:47:48.693653',
        version: '0',
        externalId: 'here:cm:namedplace:********',
        title: 'Los Angeles, CA, United States',
        lat: '34.05357',
        lon: '-118.24545',
        cityName: 'Los Angeles',
        cityCode: 'los_angeles_usa_california',
        countryCode: 'USA',
        category: 'city-town-village',
        fullAddress: null,
      },
    },
    {
      id: '58',
      createdDate: '2025-01-05T14:45:15.525167',
      lastModifiedDate: '2025-01-05T14:45:15.525168',
      version: '0',
      originalId: null,
      schoolPageId: '***********',
      schoolName: 'AccountWise',
      pageCroppedImageUrl:
        'https://storage.googleapis.com/lobox_public_images/image/original/dmy_page_v1_1Yb7FHjNwnhqadjH2R2mUc-_a84BFuvtB.png',
      majorLookupId: '170',
      majorName: 'Gerontology and Aging',
      degree: 'MIDDLE_SCHOOL',
      startDate: '2025-01-01T00:00:00',
      endDate: null,
      description: null,
      currentlyStudying: true,
      location: {
        id: '665',
        createdDate: '2025-01-05T14:45:15.525238',
        lastModifiedDate: '2025-01-05T14:45:15.52524',
        version: '0',
        externalId: 'here:cm:namedplace:********',
        title: 'Los Angeles, CA, United States',
        lat: '34.05357',
        lon: '-118.24545',
        cityName: 'Los Angeles',
        cityCode: 'los_angeles_usa_california',
        countryCode: 'USA',
        category: 'city-town-village',
        fullAddress: null,
      },
    },
  ],
  _skills: [
    {
      id: '2986',
      name: 'Food delivery',
      level: 'intermediate',
      realData: {
        name: {
          label: 'Food delivery',
          type: null,
          value: null,
        },
        id: '2986',
        level: {
          value: 'INTERMEDIATE',
          label: 'intermediate',
        },
        originalId: '1506',
        progress: 2,
        type: null,
      },
      progress: 2,
      label: 'Food delivery',
      type: null,
      skillLevel: 'INTERMEDIATE',
    },
    {
      id: '2961',
      name: 'UI',
      level: 'intermediate',
      realData: {
        name: {
          label: 'UI',
          type: null,
          value: null,
        },
        id: '2961',
        level: {
          value: 'INTERMEDIATE',
          label: 'intermediate',
        },
        originalId: '1519',
        progress: 2,
        type: null,
      },
      progress: 2,
      label: 'UI',
      type: null,
      skillLevel: 'INTERMEDIATE',
    },
    {
      id: '3044',
      name: 'MongoDB Atlas.',
      level: 'beginner',
      realData: {
        name: {
          label: 'MongoDB Atlas.',
          type: null,
          value: null,
        },
        id: '3044',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1510',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'MongoDB Atlas.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3041',
      name: 'live',
      level: 'beginner',
      realData: {
        name: {
          label: 'live',
          type: null,
          value: null,
        },
        id: '3041',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1567',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'live',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3024',
      name: 'MySQL,',
      level: 'beginner',
      realData: {
        name: {
          label: 'MySQL,',
          type: null,
          value: null,
        },
        id: '3024',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1548',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'MySQL,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2966',
      name: 'computer science',
      level: 'beginner',
      realData: {
        name: {
          label: 'computer science',
          type: null,
          value: null,
        },
        id: '2966',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1557',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'computer science',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2970',
      name: 'Server-Side Rendering (SSR),',
      level: 'beginner',
      realData: {
        name: {
          label: 'Server-Side Rendering (SSR),',
          type: null,
          value: null,
        },
        id: '2970',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1528',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Server-Side Rendering (SSR),',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3001',
      name: 'No-Code platforms.',
      level: 'beginner',
      realData: {
        name: {
          label: 'No-Code platforms.',
          type: null,
          value: null,
        },
        id: '3001',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1569',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'No-Code platforms.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3018',
      name: 'cryptocurrency,',
      level: 'beginner',
      realData: {
        name: {
          label: 'cryptocurrency,',
          type: null,
          value: null,
        },
        id: '3018',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1583',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'cryptocurrency,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2972',
      name: 'No-Code Platform Design',
      level: 'beginner',
      realData: {
        name: {
          label: 'No-Code Platform Design',
          type: null,
          value: null,
        },
        id: '2972',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1554',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'No-Code Platform Design',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2991',
      name: 'Apollo GraphQL caching',
      level: 'beginner',
      realData: {
        name: {
          label: 'Apollo GraphQL caching',
          type: null,
          value: null,
        },
        id: '2991',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1515',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Apollo GraphQL caching',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3045',
      name: 'React Router Dom (RRD),',
      level: 'beginner',
      realData: {
        name: {
          label: 'React Router Dom (RRD),',
          type: null,
          value: null,
        },
        id: '3045',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1570',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'React Router Dom (RRD),',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3032',
      name: 'Node.js,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Node.js,',
          type: null,
          value: null,
        },
        id: '3032',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1512',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Node.js,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3020',
      name: 'codebase',
      level: 'beginner',
      realData: {
        name: {
          label: 'codebase',
          type: null,
          value: null,
        },
        id: '3020',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1585',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'codebase',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3034',
      name: 'data filters',
      level: 'beginner',
      realData: {
        name: {
          label: 'data filters',
          type: null,
          value: null,
        },
        id: '3034',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1580',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'data filters',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2993',
      name: 'MongoDB,',
      level: 'beginner',
      realData: {
        name: {
          label: 'MongoDB,',
          type: null,
          value: null,
        },
        id: '2993',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1544',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'MongoDB,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2983',
      name: 'real-time price charts.',
      level: 'beginner',
      realData: {
        name: {
          label: 'real-time price charts.',
          type: null,
          value: null,
        },
        id: '2983',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1520',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'real-time price charts.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2963',
      name: 'APIs',
      level: 'beginner',
      realData: {
        name: {
          label: 'APIs',
          type: null,
          value: null,
        },
        id: '2963',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1529',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'APIs',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2999',
      name: 'Cryptocurrency exchange',
      level: 'beginner',
      realData: {
        name: {
          label: 'Cryptocurrency exchange',
          type: null,
          value: null,
        },
        id: '2999',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1555',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Cryptocurrency exchange',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2980',
      name: 'Real Estate',
      level: 'beginner',
      realData: {
        name: {
          label: 'Real Estate',
          type: null,
          value: null,
        },
        id: '2980',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1542',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Real Estate',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2988',
      name: 'cloud-based services.',
      level: 'beginner',
      realData: {
        name: {
          label: 'cloud-based services.',
          type: null,
          value: null,
        },
        id: '2988',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1535',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'cloud-based services.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3009',
      name: 'data flow',
      level: 'beginner',
      realData: {
        name: {
          label: 'data flow',
          type: null,
          value: null,
        },
        id: '3009',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1547',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'data flow',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3029',
      name: 'Redux,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Redux,',
          type: null,
          value: null,
        },
        id: '3029',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1582',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Redux,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3043',
      name: 'CSS,',
      level: 'beginner',
      realData: {
        name: {
          label: 'CSS,',
          type: null,
          value: null,
        },
        id: '3043',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1543',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'CSS,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3035',
      name: 'messaging',
      level: 'beginner',
      realData: {
        name: {
          label: 'messaging',
          type: null,
          value: null,
        },
        id: '3035',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1534',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'messaging',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2968',
      name: '(GCP).',
      level: 'beginner',
      realData: {
        name: {
          label: '(GCP).',
          type: null,
          value: null,
        },
        id: '2968',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1546',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: '(GCP).',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2989',
      name: 'Figma.',
      level: 'beginner',
      realData: {
        name: {
          label: 'Figma.',
          type: null,
          value: null,
        },
        id: '2989',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1565',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Figma.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3016',
      name: 'PostgreSQL',
      level: 'beginner',
      realData: {
        name: {
          label: 'PostgreSQL',
          type: null,
          value: null,
        },
        id: '3016',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1509',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'PostgreSQL',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3010',
      name: 'microservices architecture',
      level: 'beginner',
      realData: {
        name: {
          label: 'microservices architecture',
          type: null,
          value: null,
        },
        id: '3010',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1584',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'microservices architecture',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3000',
      name: 'machine learning',
      level: 'beginner',
      realData: {
        name: {
          label: 'machine learning',
          type: null,
          value: null,
        },
        id: '3000',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1572',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'machine learning',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3004',
      name: 'HTML,',
      level: 'beginner',
      realData: {
        name: {
          label: 'HTML,',
          type: null,
          value: null,
        },
        id: '3004',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1588',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'HTML,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2978',
      name: 'Storybook',
      level: 'beginner',
      realData: {
        name: {
          label: 'Storybook',
          type: null,
          value: null,
        },
        id: '2978',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1559',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Storybook',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3025',
      name: 'Stripe.',
      level: 'beginner',
      realData: {
        name: {
          label: 'Stripe.',
          type: null,
          value: null,
        },
        id: '3025',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1575',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Stripe.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3013',
      name: 'CloudWatch',
      level: 'beginner',
      realData: {
        name: {
          label: 'CloudWatch',
          type: null,
          value: null,
        },
        id: '3013',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1540',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'CloudWatch',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3031',
      name: 'Styled-Components,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Styled-Components,',
          type: null,
          value: null,
        },
        id: '3031',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1511',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Styled-Components,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2987',
      name: 'Results-driven',
      level: 'beginner',
      realData: {
        name: {
          label: 'Results-driven',
          type: null,
          value: null,
        },
        id: '2987',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1538',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Results-driven',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2994',
      name: 'Static Site Generation (SSG),',
      level: 'beginner',
      realData: {
        name: {
          label: 'Static Site Generation (SSG),',
          type: null,
          value: null,
        },
        id: '2994',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1539',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Static Site Generation (SSG),',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2990',
      name: 'Terraform,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Terraform,',
          type: null,
          value: null,
        },
        id: '2990',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1552',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Terraform,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3036',
      name: 'React.js,',
      level: 'beginner',
      realData: {
        name: {
          label: 'React.js,',
          type: null,
          value: null,
        },
        id: '3036',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1518',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'React.js,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3023',
      name: 'frontend',
      level: 'beginner',
      realData: {
        name: {
          label: 'frontend',
          type: null,
          value: null,
        },
        id: '3023',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1589',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'frontend',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3008',
      name: 'user dashboard',
      level: 'beginner',
      realData: {
        name: {
          label: 'user dashboard',
          type: null,
          value: null,
        },
        id: '3008',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1523',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'user dashboard',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2981',
      name: 'Fullstack Developer',
      level: 'beginner',
      realData: {
        name: {
          label: 'Fullstack Developer',
          type: null,
          value: null,
        },
        id: '2981',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1553',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Fullstack Developer',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3007',
      name: 'Single Page Applications (SPA),',
      level: 'beginner',
      realData: {
        name: {
          label: 'Single Page Applications (SPA),',
          type: null,
          value: null,
        },
        id: '3007',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1571',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Single Page Applications (SPA),',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2985',
      name: 'Bootstrap',
      level: 'beginner',
      realData: {
        name: {
          label: 'Bootstrap',
          type: null,
          value: null,
        },
        id: '2985',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1562',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Bootstrap',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2974',
      name: 'e-commerce.',
      level: 'beginner',
      realData: {
        name: {
          label: 'e-commerce.',
          type: null,
          value: null,
        },
        id: '2974',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1551',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'e-commerce.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3028',
      name: 'AWS SES',
      level: 'beginner',
      realData: {
        name: {
          label: 'AWS SES',
          type: null,
          value: null,
        },
        id: '3028',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1581',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'AWS SES',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3014',
      name: 'Google Cloud Run,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Google Cloud Run,',
          type: null,
          value: null,
        },
        id: '3014',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1564',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Google Cloud Run,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2982',
      name: 'Vue.js,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Vue.js,',
          type: null,
          value: null,
        },
        id: '2982',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1568',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Vue.js,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3046',
      name: 'Python,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Python,',
          type: null,
          value: null,
        },
        id: '3046',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1536',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Python,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2962',
      name: 'Vue.js',
      level: 'beginner',
      realData: {
        name: {
          label: 'Vue.js',
          type: null,
          value: null,
        },
        id: '2962',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1537',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Vue.js',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3042',
      name: 'TypeScript,',
      level: 'beginner',
      realData: {
        name: {
          label: 'TypeScript,',
          type: null,
          value: null,
        },
        id: '3042',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1517',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'TypeScript,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2967',
      name: 'Docker Swarm,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Docker Swarm,',
          type: null,
          value: null,
        },
        id: '2967',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1560',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Docker Swarm,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2971',
      name: 'optimizing',
      level: 'beginner',
      realData: {
        name: {
          label: 'optimizing',
          type: null,
          value: null,
        },
        id: '2971',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1521',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'optimizing',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3011',
      name: 'CMS.',
      level: 'beginner',
      realData: {
        name: {
          label: 'CMS.',
          type: null,
          value: null,
        },
        id: '3011',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1545',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'CMS.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2975',
      name: 'AWS,',
      level: 'beginner',
      realData: {
        name: {
          label: 'AWS,',
          type: null,
          value: null,
        },
        id: '2975',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1579',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'AWS,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3022',
      name: 'user-centric',
      level: 'beginner',
      realData: {
        name: {
          label: 'user-centric',
          type: null,
          value: null,
        },
        id: '3022',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1586',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'user-centric',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3003',
      name: 'QA',
      level: 'beginner',
      realData: {
        name: {
          label: 'QA',
          type: null,
          value: null,
        },
        id: '3003',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1513',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'QA',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2976',
      name: 'UX',
      level: 'beginner',
      realData: {
        name: {
          label: 'UX',
          type: null,
          value: null,
        },
        id: '2976',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1527',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'UX',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3019',
      name: 'Smart',
      level: 'beginner',
      realData: {
        name: {
          label: 'Smart',
          type: null,
          value: null,
        },
        id: '3019',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1574',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Smart',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2992',
      name: 'E-commerce online shop',
      level: 'beginner',
      realData: {
        name: {
          label: 'E-commerce online shop',
          type: null,
          value: null,
        },
        id: '2992',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1516',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'E-commerce online shop',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2964',
      name: 'Google Maps API.',
      level: 'beginner',
      realData: {
        name: {
          label: 'Google Maps API.',
          type: null,
          value: null,
        },
        id: '2964',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1525',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Google Maps API.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3021',
      name: 'Generator',
      level: 'beginner',
      realData: {
        name: {
          label: 'Generator',
          type: null,
          value: null,
        },
        id: '3021',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1549',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Generator',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2969',
      name: 'AWS.',
      level: 'beginner',
      realData: {
        name: {
          label: 'AWS.',
          type: null,
          value: null,
        },
        id: '2969',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1577',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'AWS.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3033',
      name: 'SNS.',
      level: 'beginner',
      realData: {
        name: {
          label: 'SNS.',
          type: null,
          value: null,
        },
        id: '3033',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1578',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'SNS.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3005',
      name: '(Frontend',
      level: 'beginner',
      realData: {
        name: {
          label: '(Frontend',
          type: null,
          value: null,
        },
        id: '3005',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1526',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: '(Frontend',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3038',
      name: 'Inventory management System.',
      level: 'beginner',
      realData: {
        name: {
          label: 'Inventory management System.',
          type: null,
          value: null,
        },
        id: '3038',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1573',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Inventory management System.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3027',
      name: 'Fullstack',
      level: 'beginner',
      realData: {
        name: {
          label: 'Fullstack',
          type: null,
          value: null,
        },
        id: '3027',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1533',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Fullstack',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2998',
      name: 'system performance,',
      level: 'beginner',
      realData: {
        name: {
          label: 'system performance,',
          type: null,
          value: null,
        },
        id: '2998',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1531',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'system performance,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3006',
      name: 'frontend development',
      level: 'beginner',
      realData: {
        name: {
          label: 'frontend development',
          type: null,
          value: null,
        },
        id: '3006',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1524',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'frontend development',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2965',
      name: 'React.js',
      level: 'beginner',
      realData: {
        name: {
          label: 'React.js',
          type: null,
          value: null,
        },
        id: '2965',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1522',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'React.js',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3017',
      name: 'soft delete',
      level: 'beginner',
      realData: {
        name: {
          label: 'soft delete',
          type: null,
          value: null,
        },
        id: '3017',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1576',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'soft delete',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3037',
      name: 'Enterprise Management System',
      level: 'beginner',
      realData: {
        name: {
          label: 'Enterprise Management System',
          type: null,
          value: null,
        },
        id: '3037',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1556',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Enterprise Management System',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2996',
      name: 'developing scalable web applications,',
      level: 'beginner',
      realData: {
        name: {
          label: 'developing scalable web applications,',
          type: null,
          value: null,
        },
        id: '2996',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1590',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'developing scalable web applications,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3039',
      name: 'Agriculture CRM',
      level: 'beginner',
      realData: {
        name: {
          label: 'Agriculture CRM',
          type: null,
          value: null,
        },
        id: '3039',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1566',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Agriculture CRM',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2977',
      name: 'text analysis',
      level: 'beginner',
      realData: {
        name: {
          label: 'text analysis',
          type: null,
          value: null,
        },
        id: '2977',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1507',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'text analysis',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3040',
      name: 'AgGrid.',
      level: 'beginner',
      realData: {
        name: {
          label: 'AgGrid.',
          type: null,
          value: null,
        },
        id: '3040',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1558',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'AgGrid.',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3012',
      name: 'Webpack,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Webpack,',
          type: null,
          value: null,
        },
        id: '3012',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1514',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Webpack,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2995',
      name: 'integrating',
      level: 'beginner',
      realData: {
        name: {
          label: 'integrating',
          type: null,
          value: null,
        },
        id: '2995',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1550',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'integrating',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3002',
      name: 'Hook Forms,',
      level: 'beginner',
      realData: {
        name: {
          label: 'Hook Forms,',
          type: null,
          value: null,
        },
        id: '3002',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1587',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Hook Forms,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3026',
      name: 'data visualization',
      level: 'beginner',
      realData: {
        name: {
          label: 'data visualization',
          type: null,
          value: null,
        },
        id: '3026',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1563',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'data visualization',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3015',
      name: 'REST APIs',
      level: 'beginner',
      realData: {
        name: {
          label: 'REST APIs',
          type: null,
          value: null,
        },
        id: '3015',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1591',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'REST APIs',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2997',
      name: 'mentoring',
      level: 'beginner',
      realData: {
        name: {
          label: 'mentoring',
          type: null,
          value: null,
        },
        id: '2997',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1532',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'mentoring',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '3030',
      name: 'Customer Success Management',
      level: 'beginner',
      realData: {
        name: {
          label: 'Customer Success Management',
          type: null,
          value: null,
        },
        id: '3030',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1541',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'Customer Success Management',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2984',
      name: 'AI',
      level: 'beginner',
      realData: {
        name: {
          label: 'AI',
          type: null,
          value: null,
        },
        id: '2984',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1530',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'AI',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2973',
      name: 'AI,',
      level: 'beginner',
      realData: {
        name: {
          label: 'AI,',
          type: null,
          value: null,
        },
        id: '2973',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1508',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'AI,',
      type: null,
      skillLevel: 'BEGINNER',
    },
    {
      id: '2979',
      name: 'UI/UX.',
      level: 'beginner',
      realData: {
        name: {
          label: 'UI/UX.',
          type: null,
          value: null,
        },
        id: '2979',
        level: {
          value: 'BEGINNER',
          label: 'beginner',
        },
        originalId: '1561',
        progress: 1,
        type: null,
      },
      progress: 1,
      label: 'UI/UX.',
      type: null,
      skillLevel: 'BEGINNER',
    },
  ],
  _languages: [
    {
      id: '56',
      name: 'English',
      level: 'a1',
      realData: {
        name: {
          label: 'English',
          value: null,
        },
        id: '56',
        originalId: '174',
        level: {
          value: null,
          label: 'a1',
        },
        progress: 0,
      },
      progress: 0,
      label: 'English',
      languageLevel: null,
    },
    {
      id: '57',
      name: 'Persian',
      level: 'a1',
      realData: {
        name: {
          label: 'Persian',
          value: null,
        },
        id: '57',
        originalId: '173',
        level: {
          value: null,
          label: 'a1',
        },
        progress: 0,
      },
      progress: 0,
      label: 'Persian',
      languageLevel: null,
    },
  ],
  calendarEvents: [
    {
      id: '1',
      createdDate: '2024-12-29T15:01:17',
      lastModifiedDate: '2024-12-29T15:01:17',
      version: '0',
      type: 'TODO',
      eventId: '9',
    },
  ],
  notes: [],
  reviews: [],
  socialInformationEdited: true,
  backgroundInformationEdited: false,
  expectedInformationEdited: false,
  legalInformationEdited: true,
};

export const listItem: ICandidateListItemProps = {
  id: '84',
  pageId: '***********',
  profileId: '***********',
  firstText: 'John Dow',
  secondText: '@username',
  thirdText: 'Dependency Defense Advocate',
  fourthText: 'Anatoliki Makedonia Thraki, Greece',
  avatar:
    'https://storage.googleapis.com/lobox_public_images/image/original/********************************.jpeg',
  createDateTime: '2024-12-05T13:57:09',
  usagesCount: '2',
  notesCount: '0',
  todosCount: '1',
  meetingsCount: '0',
  isManual: false,
};
