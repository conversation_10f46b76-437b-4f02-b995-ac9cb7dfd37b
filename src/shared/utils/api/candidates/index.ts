import type {
  Candidate<PERSON><PERSON><PERSON>,
  CreateCandidateAPIRequestBody,
  CandidateSocialInfoAPIRequestBody,
  CandidateDemographicInfoAPIRequestBody,
  CandidateLegalInfoAPIRequestBody,
  CandidatePreferenceInfoAPIRequestBody,
  CandidateStatusInfoAPIRequestBody,
  CandidateFormData,
  BECandidateSearchResult,
  ICandidateListItemProps,
  CandidateResumeRequestBody,
  CandidateLastViewData,
} from '@shared/types/candidates';
import type { UserApiResponse } from '@shared/types/user';
import type { ActivityProps } from '@shared/types/activityProps';
import type { MeetingDataProps } from '@shared/types/meeting';
import { ProjectProps } from '@shared/types/project';
import type { PaginateResponse } from '@shared/types/response';
import {
  jobsEndpoints,
  candidateEndpoints,
} from '../../constants/servicesEndpoints';
import request from '../../toolkit/request';
import beforeCacheCandidateInfo, {
  candidateSearchItemNormalizer,
} from '../../normalizers/beforeCacheCandidateInfo';
import beforeCacheUserInfo from '../../normalizers/beforeCacheUserInfo';
import {
  getKnownApiErrorData,
  isDuplicatedCandidateException,
} from '../utils/error';

export * from './todos';
export * from './notes';
export * from './reviews';
export * from './meetings';

export const getCandidatesList = async ({
  params,
}: {
  params?: {
    text?: string;
    page?: number;
    size?: number;
    onlyCandidates?: boolean;
  };
}): Promise<PaginateResponse<CandidateFormData>> => {
  const { data } = await request.get<PaginateResponse<CandidateAPIData>>(
    candidateEndpoints.suggestCandidates,
    {
      params,
    }
  );

  return {
    ...data,
    content: data.content.map(beforeCacheCandidateInfo),
  };
};

export const getSimilarCandidates = async ({
  params,
}: {
  params: { id: string };
}): Promise<PaginateResponse<ReturnType<typeof beforeCacheUserInfo>>> => {
  const url = candidateEndpoints.similarCandidatesById(params.id);
  const { data } = await request.get<PaginateResponse<UserApiResponse>>(url);

  return {
    ...data,
    content: data.content.map(beforeCacheUserInfo),
  };
};

export const getCandidateById = async (
  id: string
): Promise<CandidateFormData> => {
  const url = candidateEndpoints.singleCandidateById(id);
  const { data } = await request.get<CandidateAPIData>(url);

  return beforeCacheCandidateInfo(data);
};

export const createCandidate = async (params: {
  body: CreateCandidateAPIRequestBody;
}): Promise<CandidateFormData> => {
  try {
    const { data } = await request.post<CandidateAPIData>(
      candidateEndpoints.createCandidate,
      params.body
    );

    return beforeCacheCandidateInfo(data);
  } catch (e: unknown) {
    const knownApiError = getKnownApiErrorData(e);
    if (knownApiError) {
      const duplicated = knownApiError.fieldErrors.find(
        isDuplicatedCandidateException
      );
      if (duplicated) {
        const candidate = await getCandidateById(duplicated.rejectedValue);
        return {
          ...candidate,
          _isDuplicatdOnCreate: true,
        };
      }
    }
    throw e;
  }
};

export const editCandidateBasic = async (params: {
  candidateId: string;
  body: CreateCandidateAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editBasicInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateSocialInfo = async (params: {
  candidateId: string;
  body: CandidateSocialInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editSocialInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidatePreferenceInfo = async (params: {
  candidateId: string;
  body: CandidatePreferenceInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editPreferenceInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateLegalInfo = async (params: {
  candidateId: string;
  body: CandidateLegalInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editLegalInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateDemographicInfo = async (params: {
  candidateId: string;
  body: CandidateDemographicInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editDemographicInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateAdditionalInfo = async (params: {
  candidateId: string;
  body: CandidateStatusInfoAPIRequestBody;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.editAdditionalInfoById(params.candidateId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateResume = async (params: {
  candidateId: string;
  body: CandidateResumeRequestBody;
}): Promise<{ data: any }> => {
  const url = candidateEndpoints.editResumeById(params.candidateId);
  const res = await request.post<CandidateAPIData>(url, params.body);
  return res;
};

export const deleteCandidateById = async (
  id: string
): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleCandidateById(id);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const deleteCandidencyById = async (
  id: string
): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleCandidateById(id);
  const { data } = await request.delete<CandidateAPIData>(url);

  return data;
};

export const addCandidateExperience = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleExperienceById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateExperience = async (params: {
  experienceId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleExperienceById(params.experienceId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const removeCandidateExperience = async (params: {
  experienceId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleExperienceById(params.experienceId);
  const { data } = await request.delete<CandidateAPIData>(url);
  return data;
};

export const addCandidateEducation = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateEducation = async (params: {
  educationId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.educationId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const removeCandidateEducation = async (params: {
  educationId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.educationId);
  const { data } = await request.delete<CandidateAPIData>(url);
  return data;
};

export const addCandidateSkill = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleSkillById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateSkill = async (params: {
  skillId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleSkillById(params.skillId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const removeCandidateSkill = async (params: {
  skillId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleSkillById(params.skillId);
  const { data } = await request.delete<CandidateAPIData>(url);
  return data;
};

export const addCandidateLanguage = async (params: {
  candidateId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleLanguageById(params.candidateId);
  const { data } = await request.post<CandidateAPIData>(url, params.body);
  return data;
};

export const editCandidateLanguage = async (params: {
  languageId: string;
  body: any;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleLanguageById(params.languageId);
  const { data } = await request.put<CandidateAPIData>(url, params.body);
  return data;
};

export const removeCandidateLanguage = async (params: {
  languageId: string;
}): Promise<CandidateAPIData> => {
  const url = candidateEndpoints.singleEducationById(params.languageId);
  const { data } = await request.delete<CandidateAPIData>(url);
  return data;
};

export const linkJobsToCandidate = async (
  candidateId: string,
  jobIds: string[]
): Promise<any> => {
  const { data } = await request.post(jobsEndpoints.candidacy, {
    candidateId,
    jobIds,
  });
  return data;
};

export const unlinkJobsFromCandidate = async (
  candidateId: string
): Promise<any> => {
  const { data } = await request.delete(
    jobsEndpoints.getSingleCandidacyById(candidateId)
  );
  return data;
};

interface SearchCandidateArgs {
  params: {
    text?: string;
    currentEntityId?: string | number;
    occupations?: string[];
    page?: number;
    pageSize?: number;
  };
}
export const searchCandidate = async ({
  params,
}: SearchCandidateArgs): Promise<PaginateResponse<ICandidateListItemProps>> => {
  const { data } = await request.get<PaginateResponse<BECandidateSearchResult>>(
    candidateEndpoints.search,
    { params }
  );

  return {
    ...data,
    content: data.content.map(candidateSearchItemNormalizer),
  };
};

export const searchCandidateParticipations = async ({
  params,
}: SearchCandidateArgs) => {
  const { data } = await request.get<PaginateResponse<BECandidateSearchResult>>(
    candidateEndpoints.search,
    { params }
  );

  return data;
};

export const getCandidateFilters = async ({
  params,
}: {
  params: any;
}): Promise<any> => {
  const { data } = await request.get(candidateEndpoints.filter, {
    params,
  });

  return data;
};

export const searchCandidateActivities = async (params: any): Promise<any> => {
  const { data } = await request.get<PaginateResponse<ActivityProps>>(
    jobsEndpoints.searchActivities,
    { params }
  );
  return data;
};

export const searchCandidates = async (
  params: any
): Promise<PaginateResponse<any>> => {
  const { data } = await request.get<PaginateResponse<ProjectProps>>(
    candidateEndpoints.search,
    params
  );
  return data;
};

export const getUpcomingMeetings = async ({
  params,
}: {
  params: {
    candidate: string | number;
    page?: number;
    size?: number;
  };
}): Promise<MeetingDataProps> => {
  const { data } = await request.get<MeetingDataProps>(
    candidateEndpoints.upcomingMeetings,
    {
      params,
    }
  );

  return data;
};

export const getPastMeetings = async ({
  params,
}: {
  params: {
    candidate: string | number;
    page?: number;
    size?: number;
  };
}): Promise<MeetingDataProps> => {
  const { data } = await request.get<MeetingDataProps>(
    candidateEndpoints.pastMeetings,
    {
      params,
    }
  );

  return data;
};

export const getCandidateLastView = async ({
  params,
}: {
  params: { id: string };
}): Promise<CandidateLastViewData> => {
  const url = candidateEndpoints.candidateLastView(params.id);
  const { data } = await request.get<Promise<CandidateLastViewData>>(url);

  return data;
};
export const addSavedFilter = async (body: any): Promise<any> => {
  const { data } = await request.post(candidateEndpoints.addSavedFilter, body);
  return data;
};

export const editSavedFilter = async (body: any): Promise<any> => {
  const { data } = await request.put(
    candidateEndpoints.editSavedFilterById(body?.id),
    body
  );
  return data;
};

export const removeSavedFilter = async (id: string): Promise<any> => {
  const { data } = await request.delete(
    candidateEndpoints.removeSavedFilterById(id)
  );
  return data;
};

export const getAllSavedFilters = async (): Promise<any> => {
  const { data } = await request.get(candidateEndpoints.getAllSavedFilters);
  return data?.content;
};
