import type { CandidateAPIData } from '@shared/types/candidates';
import type { ProfileType } from '@shared/types/profile';
import type { BESkill } from '@shared/types/skill';
import type { Experience } from '@shared/types/experience';
import type { LanguageNormalizerType } from './languageNormalizer';

export type NormalizedCompareData = Pick<
  CandidateAPIData,
  | 'id'
  | 'preferredEmploymentType'
  | 'preferredJobTitle'
  | 'preferredExperienceLevel'
  | 'preferredLocation'
  | 'relocation'
  | 'travelRequirement'
  | 'expectedSalaryPeriod'
  | 'expectedTaxTermTitle'
  | 'expectedMarkup'
  | 'fullAddress'
> & {
  bio: string;
  email: string;
  skills: BESkill[];
  languages: LanguageNormalizerType[];
  experiences: Experience[];
};

export default function compareNormalizer(
  data: Array<CandidateAPIData | ProfileType>
) {
  return data.reduce((acc, curr) => {
    if ((curr as CandidateAPIData).profile) {
      const candidate = curr as CandidateAPIData;
      return [
        ...acc,
        {
          id: candidate.id,
          //   bio: candidate.profile.bio,
          bio: null,
          email: candidate.profile.email,
          skills: candidate.profile.skills,
          languages: candidate.profile.languages,
          experiences: candidate.profile.experiences,
          preferredEmploymentType: candidate.preferredEmploymentType,
          preferredJobTitle: candidate.preferredJobTitle,
          preferredExperienceLevel: candidate.preferredExperienceLevel,
          preferredLocation: candidate.preferredLocation,
          relocation: candidate.relocation,
          travelRequirement: candidate.travelRequirement,
          expectedSalaryPeriod: candidate.expectedSalaryPeriod,
          expectedTaxTermTitle: candidate.expectedTaxTermTitle,
          expectedMarkup: candidate.expectedMarkup,
          fullAddress: candidate.fullAddress,
        },
      ];
    }
    const applicant = curr as ProfileType;
    return [
      ...acc,
      {
        id: applicant.id ?? '',
        bio: applicant.bio,
        email: applicant.email,
        skills: applicant.skills,
        languages: applicant.languages,
        experiences: applicant.experiences,
        preferredEmploymentType: applicant.preferredEmploymentType,
        preferredJobTitle: applicant.preferredJobTitle,
        preferredExperienceLevel: applicant.preferredExperienceLevel,
        preferredLocation: applicant.preferredLocation,
        relocation: applicant.relocation,
        travelRequirement: applicant.travelRequirement,
        expectedSalaryPeriod: applicant.expectedSalaryPeriod,
        expectedTaxTermTitle: applicant.expectedTaxTermTitle,
        expectedMarkup: applicant.expectedMarkup,
        fullAddress: applicant.fullAddress,
      },
    ];
  }, [] as NormalizedCompareData[]);
}
