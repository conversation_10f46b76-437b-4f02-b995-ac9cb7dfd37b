import type {
  CallbackParams,
  MultiStepFormProps,
} from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import type { colorsKeys } from 'shared/uikit/helpers/theme';
import type { JobSkillProps, LanguageProps } from './jobsProps';
import type { CategoryType } from './lookup';
import type { ProjectProps } from './project';

export type WorkPlaceType = 'ON_SITE' | 'HYBRID' | 'REMOTE';
export type EmploymentType =
  | 'FULL_TIME'
  | 'PART_TIME'
  | 'CONTRACT'
  | 'CONTRACT_TO_HIRE'
  | 'SELF_EMPLOYED'
  | 'FREELANCE'
  | 'TEMPORARY'
  | 'VOLUNTEER'
  | 'INTERNSHIP'
  | 'APPRENTICESHIP'
  | 'SEASONAL';
export type ResponseTimeType =
  | 'IMMEDIATE'
  | 'WITHIN_1_DAY'
  | 'WITHIN_2_DAYS'
  | 'WITHIN_3_DAYS'
  | 'WITHIN_1_WEEK'
  | 'WITHIN_2_WEEKS'
  | 'WITHIN_1_MONTH'
  | 'WITHIN_1_YEAR'
  | 'FLEXIBLE';
export type PriorityType = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
export type DaysType =
  | 'MONDAY'
  | 'TUESDAY'
  | 'WEDNESDAY'
  | 'THURSDAY'
  | 'FRIDAY'
  | 'SATURDAY'
  | 'SUNDAY';
export type ExperienceLevelType =
  | 'ENTRY_LEVEL'
  | 'MIDDLE_LEVEL'
  | 'SENIOR_LEVEL'
  | 'PRINCIPAL_LEVEL'
  | 'ASSOCIATE'
  | 'DIRECTOR'
  | 'EXECUTIVE'
  | 'INTERNSHIP';
export type EducationDegreeType =
  | 'BACHELOR'
  | 'MASTER'
  | 'DOCTORAL'
  | 'ASSOCIATE'
  | 'POSTDOCTORAL'
  | 'MIDDLE_SCHOOL'
  | 'HIGH_SCHOOL'
  | 'ELEMENTARY'
  | 'PROFESSIONAL'
  | 'DIPLOMA'
  | 'CERTIFICATE'
  | 'HONORARY'
  | 'NON';
export type ContractDurationType =
  | 'THREE_MONTHS'
  | 'SIZ_MONTHS'
  | 'ONE_YEAR'
  | 'FULL_TIME';
export type PeriodType =
  | 'HOURLY'
  | 'DAILY'
  | 'WEEKLY'
  | 'BIWEEKLY'
  | 'MONTHLY'
  | 'YEARLY';
export type WillingToTravelType =
  | 'YES'
  | 'NO'
  | 'OCCASIONALLY'
  | 'CONSIDERABLE';

export interface MultiStepFormStepProps {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  getStepHeaderProps: Exclude<
    MultiStepFormProps['getStepHeaderProps'],
    undefined
  >;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
  getValidationSchema?: Exclude<
    MultiStepFormProps['getValidationSchema'],
    undefined
  >;
  onSuccess?: Exclude<MultiStepFormProps['onSuccess'], undefined>;
  formName?: string;
  url?: string;
  transform?: Exclude<MultiStepFormProps['transform'], undefined>;
}

export type MultiStepFormFooterProps = Pick<
  CallbackParams,
  | 'isSubmitting'
  | 'setStep'
  | 'isValid'
  | 'status'
  | 'validateForm'
  | 'step'
  | 'values'
  | 'setValues'
> & {
  isSubmitStep?: boolean;
  jobId?: string;
};

export interface CreateEntityModalProps<T> {
  data?: T;
}

export type DBStaticItemProps<T> = {
  label: string;
  value?: T;
};

export type PipelineFollowUpPeriodType =
  | '_3_DAYS'
  | '_5_DAYS'
  | '_7_DAYS'
  | '_14_DAYS';

export type PipelineStageType = 'HIRING' | 'ONBOARDING';

export interface PipelineTemplateProps {
  autoReplyTitle?: string;
  autoReplyMessage?: string;
  hasFollowUpMessage?: boolean;
  followUpTitle?: string;
  followUpMessage?: string;
  followupMessagePeriod?: PipelineFollowUpPeriodType;
}

export interface PipelineProps extends PipelineTemplateProps {
  id?: string;
  title?: string;
  type?:
    | 'REVIEW'
    | 'INTERVIEW'
    | 'OFFERED'
    | 'HIRED'
    | 'ON_BOARDED'
    | 'CUSTOMIZE';
  applicantTrack?: boolean;
  order?: number;
  color?: colorsKeys;
  stageType?: PipelineStageType;
  customizeStageType?: PipelineStageType;
  dateTime?: string;
}

export type AutoReplyProps = Pick<
  PipelineProps,
  | 'autoReplyMessage'
  | 'autoReplyTitle'
  | 'followUpMessage'
  | 'followUpTitle'
  | 'followupMessagePeriod'
  | 'hasFollowUpMessage'
>;

export type TemplateDynamicValueType =
  | 'APPLICANT_NAME'
  | 'PAGE_NAME'
  | 'JOB_TITLE'
  | 'RECRUITER_NAME';

export interface FiltersDataProps {
  titles: string[];
  categories: CategoryType[];
  cities: { code: string; name: string }[];
  skills: JobSkillProps[];
  languages: LanguageProps[];
  travelRequirements?: string[];
  projects: Pick<ProjectProps, 'id' | 'title'>[];
  salaryRange: {
    currencyCode: {};
    id: string;
    max: string;
    min: string;
    period: string;
  };
  clientIds?: { id: string; title: string }[];
  vendorIds?: { id: string; title: string }[];
  collaborators: { id: string; name: string; surname: string }[];
  pointOfContacts: { id: string; name: string; surname: string }[];
  creators: { id: string; name: string; surname: string }[];
  owners: { id: string; name: string; surname: string }[];
  workAuthorization: { title: string; id: string }[];
  tags: string[];
  hashtags: string[];
  jobs: { id: string; title: string }[];
  statuses: string[];
}

export type RemindDurationType =
  | '_5_MIN_BEFORE'
  | '_10_MIN_BEFORE'
  | '_15_MIN_BEFORE'
  | '_30_MIN_BEFORE'
  | '_1_HOUR_BEFORE'
  | '_1_DAY_BEFORE';
