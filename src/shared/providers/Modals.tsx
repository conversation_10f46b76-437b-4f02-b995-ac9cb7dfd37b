'use client';

import React, { useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  useGlobalDispatch,
  useGlobalState,
} from 'shared/contexts/Global/global.provider';
import {
  selectIsOpen,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import { PresenceAnimationWrapper } from 'shared/components/molecules/PresenceAnimationWrapper';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import { useAsyncPickerModalProps } from '@shared/components/Organism/AsyncPickerModal';
import { usePathname, useSearchParams } from 'next/navigation';

const ProfileAboutMultiStepForm = dynamic(
  () =>
    import(
      'shared/components/Organism/MultiStepForm/ProfileAboutEdit/ProfileAboutMultiStepForm'
    ),
  { ssr: false }
);
const ProfileSectionsMultiStepForm = dynamic(
  () =>
    import(
      'shared/components/Organism/MultiStepForm/ProfileSections/ProfileSectionsMultiStepForm'
    ),
  { ssr: false }
);

const ProfileUploadResume = dynamic(
  () =>
    import(
      'shared/components/Organism/MultiStepForm/ProfileUploadResume/ProfileUploadResume'
    ),
  { ssr: false }
);

const CreateEntityPanel = dynamic(
  () => import('shared/components/Organism/CreateEntityPanel'),
  { ssr: false }
);

const NineDotPanel = dynamic(
  () => import('shared/components/Organism/NineDotPanel'),
  { ssr: false }
);

const CandidateManager = dynamic(
  () => import('shared/components/Organism/CandidateManager'),
  { ssr: false }
);

const CreateCandidateForm = dynamic(
  () => import('shared/components/Organism/MultiStepForm/CreateCandidateForm'),
  {
    ssr: false,
    loading: () => <>CreateCandidateForm is loading...</>,
  }
);

const AsyncPickerModal = dynamic(
  () => import('@shared/components/molecules/BaseAsyncPickerModal'),
  { ssr: false }
);

const PostReactionsModal = dynamic(
  () => import('@shared/components/Organism/FeedCard/modals/PostReactions'),
  { ssr: false }
);

const PostCommentsModal = dynamic(
  () => import('@shared/components/Organism/FeedCard/modals/Comments'),
  { ssr: false }
);

const NotificationModal = dynamic(
  () =>
    import('@shared/components/layouts/AppLayout/partials/NotificationModal'),
  { ssr: false }
);
const ConfirmBlockModal = dynamic(
  () => import('@shared/components/Organism/ConfirmBlockModal'),
  { ssr: false }
);
const CheckoutModal = dynamic(
  () => import('@shared/components/Organism/MultiStepForm/CheckoutForm'),
  { ssr: false }
);
const AutomationModal = dynamic(
  () => import('@shared/components/Organism/AutomationModal/AutomationModal'),
  { ssr: false }
);
const AutoMoveModal = dynamic(
  () => import('@shared/components/Organism/AutomationModal/AutoMoveModal'),
  { ssr: false }
);
const RequirementsModal = dynamic(
  () => import('@shared/components/Organism/AutomationModal/RequirementsModal'),
  { ssr: false }
);

const Modals = () => {
  const profileSectionsState = useMultiStepFormState('editProfileSections');
  const profileAboutEditState = useMultiStepFormState('profileAboutEdit');
  const profileResumeUploadState = useMultiStepFormState('resumeUpload');
  const createCandidateForm = useMultiStepFormState('createCandidateForm');
  const isNineDotPaneOpen = useNineDotPanelState(selectIsOpen);
  const candidateManager = useGlobalState('candidateManager');
  const isCreateEntityPanelOpen = useGlobalState('isCreateEntityPanelOpen');
  const asyncPickerProps = useAsyncPickerModalProps();
  const commentModalData = useGlobalState('commentModalData');
  const searchParams = useSearchParams();
  const reactionsModalData = useGlobalState('reactionsModalData');
  const isOpenNotificationModal = useGlobalState('isOpenNotificationModal');
  const isOpenConfirmBlockModal = useGlobalState('confirmBlockModal')?.isOpen;
  const isEditProfileModalOpen = searchParams.get('openEditModal') === 'true';
  const checkoutModalState = useMultiStepFormState('checkout');
  const automationModalState = useGlobalState('automationModal');
  const autoMoveModalState = useGlobalState('autoMoveModal');
  const pathname = usePathname();
  const globalDispatch = useGlobalDispatch();
  const requirementsModalState = useGlobalState('requirementsModal');

  useEffect(() => {
    if (commentModalData) {
      globalDispatch({
        type: 'TOGGLE_COMMENTS_MODAL',
        payload: undefined,
      });
    }
    if (reactionsModalData) {
      globalDispatch({
        type: 'TOGGLE_FEED_REACTIONS_MODAL',
        payload: undefined,
      });
    }
    if (isOpenNotificationModal) {
      globalDispatch({
        type: 'SET_IS_OPEN_NOTIFICATION_PANEL',
        payload: false,
      });
    }
  }, [pathname]);

  return (
    <>
      <PresenceAnimationWrapper isOpen={profileAboutEditState?.isOpen}>
        <ProfileAboutMultiStepForm />
      </PresenceAnimationWrapper>
      {profileResumeUploadState?.isOpen && <ProfileUploadResume />}
      <PresenceAnimationWrapper
        isOpen={profileSectionsState?.isOpen || isEditProfileModalOpen}
      >
        <ProfileSectionsMultiStepForm />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={isCreateEntityPanelOpen}>
        <CreateEntityPanel />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={isNineDotPaneOpen}>
        <NineDotPanel />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={createCandidateForm?.isOpen}>
        <CreateCandidateForm />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={candidateManager?.isOpen}>
        <CandidateManager />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={!!asyncPickerProps}>
        {asyncPickerProps ? <AsyncPickerModal {...asyncPickerProps} /> : null}
      </PresenceAnimationWrapper>
      {!!commentModalData && <PostCommentsModal />}
      {!!reactionsModalData && <PostReactionsModal />}
      <PresenceAnimationWrapper isOpen={isOpenNotificationModal}>
        <NotificationModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={isOpenConfirmBlockModal}>
        <ConfirmBlockModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={checkoutModalState?.isOpen}>
        <CheckoutModal {...checkoutModalState} />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={automationModalState?.isOpen}>
        <AutomationModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={autoMoveModalState?.isOpen}>
        <AutoMoveModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={requirementsModalState?.isOpen}>
        <RequirementsModal />
      </PresenceAnimationWrapper>
    </>
  );
};

export default Modals;
