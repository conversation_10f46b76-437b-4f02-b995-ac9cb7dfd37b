'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import cnj from 'shared/uikit/utils/cnj';
import Drawer from 'shared/uikit/Drawer';
import Flex from 'shared/uikit/Flex';
import Media from 'shared/uikit/Media';
import event from 'shared/utils/toolkit/event';
import { routeNames } from 'shared/utils/constants/routeNames';
import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import {
  useGlobalDispatch,
  useGlobalState,
} from 'shared/contexts/Global/global.provider';
import { MAIN_CENTER_WRAPPER_ID } from 'shared/constants/enums';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import useIsPagePublished from 'shared/hooks/useIsPagePublished';
import SocialConnectionsModal from 'shared/components/Organism/SocialConnectionsModal';
import ShareEntityTabbedModal from 'shared/components/Organism/ShareEntityTabbedModal';
import Header from 'shared/components/layouts/AppLayout/AppLayout.Header';
import LeftNavigation from 'shared/components/layouts/AppLayout/AppLayout.LeftNavigation';
import Bottombar from 'shared/components/layouts/AppLayout/AppLayout.Bottombar';
import ViewMedia from 'shared/components/Organism/Message/components/organism/ViewMedia';
import Loading from 'shared/components/layouts/AppLayout/AppLayout.ChatPanel/partials/ClosedRightPanelItems.loading';
import dynamic from 'next/dynamic';
import useLocation from 'shared/utils/hooks/useLocation';
import InvitiePeopleMultiStepForm from 'shared/components/Organism/MultiStepForm/InvitePeople/InvitePeople';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import ListOfInvitationFailuresModal from 'shared/components/Organism/ListOfInvitationFailuresModal';
import ListOfInvitedPeople from 'shared/components/Organism/ListOfInvitedPeople';
import useScrollToTopEventListener from 'shared/utils/useScrollToTopEventListener';
import eventKeys from 'shared/constants/event-keys';
import { mutableStore } from 'shared/constants/mutableStore';
import { PresenceAnimationWrapper } from 'shared/components/molecules/PresenceAnimationWrapper';
import useMedia from 'shared/uikit/utils/useMedia';
import useRegisterPushNotificationToken from '@shared/utils/hooks/useRegisterPushNotificationToken';
import { openNineDotPanel } from '@shared/stores/nineDotPanelStore';
import classes from './index.module.scss';

const ChatPanel = dynamic(
  () => import('shared/components/layouts/AppLayout/AppLayout.ChatPanel'),
  {
    ssr: false,
    loading: () => <Loading />,
  }
);

const FeedbackModal = dynamic(
  () => import('shared/components/Organism/FeedbackModal'),
  {
    ssr: false,
  }
);
const CreatePostModal = dynamic(
  () => import('shared/components/Organism/CreatePostModal'),
  { ssr: false }
);
const ReportModal = dynamic(
  () => import('shared/components/Organism/ReportModal'),
  { ssr: false }
);
const CreateJobModalInUserProject = dynamic(
  () => import('shared/components/Organism/CreateJobModalInUserProject'),
  { ssr: false }
);
const ProfileMenu = dynamic(
  () => import('shared/components/layouts/AppLayout/AppLayout.ProfileMenu'),
  { ssr: false }
);
const CreateProjectFrom = dynamic(
  () => import('shared/components/Organism/MultiStepForm/CreateProjectForm'),
  {
    ssr: false,
  }
);
const CreateJobForm = dynamic(
  () => import('shared/components/Organism/MultiStepForm/CreateJobForm'),
  {
    ssr: false,
  }
);
const LinkJobForm = dynamic(
  () => import('shared/components/Organism/MultiStepForm/LinkJobForm'),
  {
    ssr: false,
  }
);
const DeleteEntityModal = dynamic(
  () => import('shared/components/Organism/MultiStepForm/DeleteEntityModal'),
  {
    ssr: false,
  }
);
const EditAssigneeModal = dynamic(
  () => import('@shared/components/molecules/EditAssigneeModal'),
  {
    ssr: false,
  }
);
const CompareModal = dynamic(
  () => import('shared/components/Organism/CompareModal'),
  {
    ssr: false,
  }
);
const AppLayout: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { searchParams, pathname, preserveSearchParams } = useLocation();
  const { replace } = useRouter();
  const { isOpen: isInviteFormOpen } = useMultiStepFormState('invitePeople');
  const { isOpen: isCreateProjectopen, ...createProjectProps } =
    useMultiStepFormState('createProjectForm');
  const { isOpen: isCreateJobForm, data } =
    useMultiStepFormState('createJobForm');
  const { isOpen: isLinkJobFormopen, ...linkJobProps } =
    useMultiStepFormState('linkJobForm');
  const { isOpen: isDeleteEntitypen } =
    useMultiStepFormState('deleteEntityModal');
  const { reFetchAppObject, authUser } = useGetAppObject();
  const isPagePublished = useIsPagePublished();
  const globalDispatch = useGlobalDispatch();
  const objectNetworkModal = useGlobalState('objectNetworkModal');
  const isFeedbackModalOpen = useGlobalState('isFeedbackModalOpen');
  const isOpenLeftPanel = useGlobalState('isOpenLeftPanel');
  const isOpenProfilePanel = useGlobalState('isOpenProfilePanel');
  const { isMoreThanTablet } = useMedia();
  const { isOpen: isOpenReportModal } = useGlobalState('reportModal');
  const { isOpen: isOpenFailuresModal } = useGlobalState(
    'invitationFailuresModal'
  );
  const { isOpen: isOpenInvitationsList } = useGlobalState(
    'invitationsListModal'
  );
  const isOpenCreateJobModalInUserProject = useGlobalState(
    'isOpenCreateJobModalInUserProject'
  );
  const viewMedia = useGlobalState('viewMedia');
  const isLoggedIn = useAuthState('isLoggedIn');
  const editAssigneeData = useGlobalState('editAssigneeModalData');

  const isUnAuthAvailability =
    !isLoggedIn && pathname?.includes(routeNames.schedulesAvailability.main);

  const { isOpen: isShareEntityModalOpen } = useGlobalState(
    'shareEntityTabbedModal'
  );
  const compareModalData = useGlobalState('compareModal');
  useScrollToTopEventListener();
  useRegisterPushNotificationToken();

  useEffect(() => {
    if (!authUser?.publicSetting) {
      reFetchAppObject();
    }
  }, [authUser]);

  const closeLeftPanel = () => {
    globalDispatch({
      type: 'CLOSE_LEFT_PANEL',
    });
  };
  useEffect(() => {
    event.trigger(eventKeys.scrollToTopFeedList);
    globalDispatch({
      type: 'RESET_ON_NAVIGATION',
    });
    closeLeftPanel();
  }, [pathname]);

  const openTarget = searchParams.get('open-target');
  useEffect(() => {
    if (openTarget === 'ticket') {
      openNineDotPanel({ isOpen: true, defaultActiveStep: 'SUPPORT' });
      replace(preserveSearchParams(pathname, { delete: ['open-target'] }));
    }
  }, [openTarget]);

  const toggleLeftPanelHandler = () => {
    globalDispatch({
      type: isOpenLeftPanel ? 'CLOSE_LEFT_PANEL' : 'OPEN_LEFT_PANEL',
    });
  };

  const visibleChatPanel =
    isLoggedIn && pathname !== routeNames.messages && isPagePublished;

  mutableStore.hasMessagePanelOnRightSide = visibleChatPanel;

  return (
    <>
      <Flex className={classes.appRoot}>
        <Header toggleLeftPanel={toggleLeftPanelHandler} />
        <Flex
          className={cnj(
            classes.appMain,
            isUnAuthAvailability && classes.noMarginTop
          )}
        >
          {isLoggedIn && isPagePublished && (
            <Flex className={classes.mainLeft}>
              <LeftNavigation closeLeftPanel={closeLeftPanel} autoWide narrow />
            </Flex>
          )}
          <Flex
            id={MAIN_CENTER_WRAPPER_ID}
            className={cnj(
              classes.mainCenter,
              visibleChatPanel && classes.mainCenterLoggedIn
            )}
          >
            {children}
          </Flex>
          {visibleChatPanel && (
            <Flex
              className={cnj(
                classes.mainRight,
                isOpenLeftPanel && classes.dark
              )}
            >
              <Media greaterThan="tablet">
                <ChatPanel />
              </Media>
            </Flex>
          )}
          {isOpenLeftPanel && (
            <Media greaterThan="tablet">
              <Drawer sliderDir="left" onBackDropClick={closeLeftPanel}>
                <LeftNavigation
                  closeLeftPanel={closeLeftPanel}
                  narrow={false}
                  wide
                />
              </Drawer>
            </Media>
          )}
          {isMoreThanTablet && (
            <PresenceAnimationWrapper isOpen={isOpenProfilePanel}>
              <ProfileMenu />
            </PresenceAnimationWrapper>
          )}
        </Flex>
        {isLoggedIn && (
          <Media at="tablet">
            <Bottombar />
          </Media>
        )}
      </Flex>
      {isFeedbackModalOpen && <FeedbackModal />}
      {objectNetworkModal?.isOpen && <SocialConnectionsModal />}
      {isOpenFailuresModal && <ListOfInvitationFailuresModal />}
      {isOpenInvitationsList && <ListOfInvitedPeople />}
      {isShareEntityModalOpen ? (
        <ShareEntityTabbedModal />
      ) : (
        <CreatePostModal />
      )}

      {viewMedia?.isOpen && (
        <ViewMedia
          medias={viewMedia?.medias}
          selectedItem={viewMedia?.selectedItem}
        />
      )}
      {isOpenReportModal && <ReportModal />}
      {isOpenCreateJobModalInUserProject && <CreateJobModalInUserProject />}
      {isInviteFormOpen && <InvitiePeopleMultiStepForm />}
      {isCreateJobForm && <CreateJobForm initialData={data as any} />}
      {isCreateProjectopen && <CreateProjectFrom {...createProjectProps} />}
      {isLinkJobFormopen && <LinkJobForm data={linkJobProps.data} />}
      {isDeleteEntitypen && <DeleteEntityModal />}
      {editAssigneeData?.open && <EditAssigneeModal {...editAssigneeData} />}
      {compareModalData?.open && <CompareModal {...compareModalData} />}
    </>
  );
};
export default AppLayout;
