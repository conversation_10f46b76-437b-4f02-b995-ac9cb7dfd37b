import type { CallbackParams } from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import TextInputAddon from '@shared/uikit/TextInput/TextInputAddon';
import { useMemo } from 'react';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { socialMediaOrigins } from '@shared/constants/socialmedia';
import classes from '../../../CreateCandidate.module.scss';

const CandidateSocialBody: React.FC<CallbackParams> = () => {
  const { t } = useTranslation();

  const formGroups = useMemo(
    () => [
      {
        name: 'form_group_1',
        formGroup: {
          title: t('social_profiles'),
          className: classes.firstFormGroupLabel,
          formSection: true,
        },
        wrapStyle: classes.formItemWrapStyle,
        cp: () => null,
      },
      {
        name: 'linkedinUsername',
        cp: 'input',
        forceVisibleError: true,
        label: t('username'),
        wrapStyle: classes.formItemWrapStyle,
        leftAddon: (
          <TextInputAddon
            left
            label={socialMediaOrigins.linkedin}
            icon="linkedin-line"
            className={classes.inputAddon}
            iconClassName={classes.addonIcon}
          />
        ),
      },
      {
        name: 'facebookUsername',
        cp: 'input',
        forceVisibleError: true,
        label: t('username'),
        wrapStyle: classes.formItemWrapStyle,
        leftAddon: (
          <TextInputAddon
            left
            label={socialMediaOrigins.facebook}
            icon="facebook-line"
            className={classes.inputAddon}
            iconClassName={classes.addonIcon}
          />
        ),
      },
      {
        name: 'twitterUsername',
        cp: 'input',
        forceVisibleError: true,
        label: t('username'),
        wrapStyle: classes.formItemWrapStyle,
        leftAddon: (
          <TextInputAddon
            left
            label={socialMediaOrigins.x}
            icon="x-twitter-line"
            className={classes.inputAddon}
            iconClassName={classes.addonIcon}
          />
        ),
      },
      {
        name: 'otherUrl',
        cp: 'input',
        forceVisibleError: true,
        wrapStyle: classes.formItemWrapStyle,
        label: t('other_profile_url'),
        helperText: t('website_helper'),
      },
      {
        name: 'form_group_2',
        formGroup: {
          title: t('other_contacts'),
          className: classes.formGroupLabel,
          formSection: true,
        },
        wrapStyle: classes.formItemWrapStyle,
        cp: () => null,
      },
      /** LINE 5 */
      {
        name: 'cellNumber',
        cp: 'phoneInput',
        forceVisibleError: true,
        label: t('cell_number'),
        isFirstHalfWidth: true,
      },
      {
        name: 'workNumber',
        cp: 'phoneInput',
        forceVisibleError: true,
        label: t('work_number'),
        isSecondHalfWidth: true,
      },
      /** LINE 6 */
      {
        name: 'homeNumber',
        cp: 'phoneInput',
        forceVisibleError: true,
        label: t('home_number'),
        isFirstHalfWidth: true,
      },
      {
        name: 'skypeId',
        cp: 'input',
        label: t('skype_id'),
        forceVisibleError: true,
        isSecondHalfWidth: true,
      },
      {
        name: 'fullAddress',
        cp: 'richtext',
        forceVisibleError: true,
        label: `${t('full_address')} ${t('(optional)')}`,
        // maxLength: DESCRIPTION_MAX_LENGTH,
        className: 'flex-1',
        visibleOptionalLabel: false,
        showEmoji: false,
      },
    ],
    [t]
  );
  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      <DynamicFormBuilder className={classes.formBuilder} groups={formGroups} />
    </ModalTransitionWrapper>
  );
};

export default CandidateSocialBody;
