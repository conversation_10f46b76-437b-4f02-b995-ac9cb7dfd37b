import type { FC, PropsWithChildren } from 'react';

import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import cnj from '@shared/uikit/utils/cnj';
import Typography from '@shared/uikit/Typography';
import ComponentBuilder from '@shared/uikit/Form/ComponentBuilder';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { GENDER_VALUES } from '@shared/utils/constants/enums';
import Form from '@shared/uikit/Form';
import Button from '@shared/uikit/Button';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Tooltip from '@shared/uikit/Tooltip';
import { useManagerContext } from '../CandidateManager.context';
import classes from './JobControl.module.scss';

interface Props {
  className?: string;
}
export const CandidateManagerJobControl: FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const { candidate } = useManagerContext();
  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();

  return (
    <Tooltip
      trigger={
        <Form initialValues={{}} className="contents">
          <Flex flexDir="row" className={cnj(className, classes.wrapper)}>
            <Flex>
              <DynamicFormBuilder
                groups={[
                  {
                    name: 'gender',
                    cp: 'dropdownSelect',
                    label: t('application'),
                    inputWrapClassName: '!border-0',
                    options: Object.values(GENDER_VALUES),
                    dsiabled: true,
                  },
                ]}
              />
            </Flex>
            <DividerVertical distance={2} height={56} />
            <Flex flexDir="row" className={classes.inner}>
              <Button disabled label={t('review')} schema="semi-transparent" />
              <Button
                disabled
                label={t('reject')}
                schema="error-semi-transparent"
              />
            </Flex>
          </Flex>
        </Form>
      }
    >
      {t('coming_soon')}
    </Tooltip>
  );
};
