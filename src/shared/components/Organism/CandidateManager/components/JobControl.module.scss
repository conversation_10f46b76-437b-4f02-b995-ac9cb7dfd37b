@import '/src/shared/theme/theme.scss';

@layer organism {
  .wrapper {
    background-color: colors(background);
    border-bottom-left-radius: variables(xLargeGutter) * 0.5;
    border-bottom-right-radius: variables(xLargeGutter) * 0.5;
    border: 1px solid colors(techGray_20);
    overflow: hidden;
    height: 63px;
    z-index: 1;
    min-height: min-content;

    .inner {
      flex: 1;
      gap: variables(gutter) * 0.5;
      padding: variables(xLargeGutter) * 0.5;
      flex-direction: row;
      justify-content: end;
      align-items: center;
    }
  }
}
