import Tabs from 'shared/components/Organism/Tabs';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';
import cnj from '@shared/uikit/utils/cnj';
import Carousel from '@shared/uikit/Carousel';
import type { JSX } from 'react';
import classes from './CandidateManager.module.scss';
import { CandidateManagerDetailsView } from './components/DetailsView';
import { CandidateManagerJobControl } from './components/JobControl';
import { CandidateManagerPaginationHeader } from './components/PaginationHeader';
import { UpcomingMeetingsNotice } from './components/UpcomingMeetingsNotice';
import type { CandidateManagerTabkeys } from './tabs';
import CandidateManagerPanels from './tabs';
import { useManagerContext } from './CandidateManager.context';
import FilterBody from './components/FiltersBody/FilterBody.compoent';
import useCandidateManagerNotesFilters from './tabs/tab1.notes/useCandidateManagerNotesFilters';
import useCandidateManagerTodosFilters from './tabs/tab2.todos/useCandidateManagerTodosFilters';
import CreateCandidateFromUser from './tabs/CreateCandidateFromUser';

const tabs: Array<{ path: CandidateManagerTabkeys; title: string }> = [
  {
    path: 'notes',
    title: 'notes',
  },
  {
    path: 'todos',
    title: 'todos',
  },
  {
    path: 'meetings',
    title: 'meetings',
  },
  {
    path: 'documents',
    title: 'documents',
  },
  {
    path: 'reviews',
    title: 'reviews',
  },
  {
    path: 'skillboard',
    title: 'Skill Board',
  },
  {
    path: 'threads',
    title: 'threads',
  },
  {
    path: 'emails',
    title: 'emails',
  },
  {
    path: 'test',
    title: 'test',
  },
];

const CandidateManagerLayout = (): JSX.Element => {
  const { t } = useTranslation();
  const { isCandidate, isLoading, activeFilter, selectedTab, setSelectedTab } =
    useManagerContext();
  const notesFilterGroups = useCandidateManagerNotesFilters();
  const todosFilterGroups = useCandidateManagerTodosFilters();

  return (
    <Flex className="gap-20 overflow-hidden flex-1">
      <UpcomingMeetingsNotice />
      <CandidateManagerPaginationHeader />
      <Flex flexDir="row" className="gap-20 flex-1 overflow-hidden">
        <Flex className={cnj(classes.scrollFix, 'flex-1 relative')}>
          <CandidateManagerDetailsView className="flex-1" />
          <Flex className="sticky z-10 bottom-0 w-full !bg-tooltipText">
            <CandidateManagerJobControl />
          </Flex>
        </Flex>
        {!isLoading && !isCandidate ? (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <CreateCandidateFromUser />
          </Flex>
        ) : activeFilter === selectedTab && activeFilter === 'notes' ? (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <FilterBody rootName="notes" groups={notesFilterGroups} />
          </Flex>
        ) : activeFilter === selectedTab && activeFilter === 'todos' ? (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <FilterBody rootName="todos" groups={todosFilterGroups} />
          </Flex>
        ) : (
          <Flex className="w-[40%] bg-background rounded-[12px] overflow-hidden">
            <Carousel>
              <Tabs
                className="!pt-4"
                activePath={selectedTab}
                onChangeTab={setSelectedTab}
                tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
                divide
              />
            </Carousel>
            <CandidateManagerPanels active={selectedTab} />
          </Flex>
        )}
      </Flex>
    </Flex>
  );
};

export default CandidateManagerLayout;
