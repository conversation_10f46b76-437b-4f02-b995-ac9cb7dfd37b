import type { CandidateFormData } from '@shared/types/candidates';
import type { Dispatch, SetStateAction } from 'react';
import { createContext, useContext } from 'react';
import type { CandidateManagerTabkeys } from './tabs';

interface ManagerFilterContext {
  todos: Record<string, any>;
  notes: Record<string, any>;
}

export type FilterKeys = keyof ManagerFilterContext;
export type ActiveFilterKeys = FilterKeys | undefined;

export interface CandidateManagerContextValue {
  candidate?: CandidateFormData;
  isCandidate?: boolean;
  isLoading: boolean;
  selectedTab: CandidateManagerTabkeys;
  setSelectedTab: Dispatch<SetStateAction<CandidateManagerTabkeys>>;
  activeFilter?: ActiveFilterKeys;
  setActiveFilter: Dispatch<SetStateAction<ActiveFilterKeys>>;
  filters: ManagerFilterContext;
  setFilters: Dispatch<SetStateAction<ManagerFilterContext>>;
}

const CandidateManagerContext = createContext<CandidateManagerContextValue>({
  isLoading: true,
  selectedTab: 'todos',
} as CandidateManagerContextValue);

export default CandidateManagerContext.Provider;

export function useManagerContext() {
  return useContext(CandidateManagerContext);
}
