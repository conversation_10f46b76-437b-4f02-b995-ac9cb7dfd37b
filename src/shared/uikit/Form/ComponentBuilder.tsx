import type { ComponentProps } from 'react';
import React from 'react';
import isString from 'lodash/isString';
import dynamic from 'next/dynamic';
import TextInput from '../TextInput';
import FieldWrapper from './FieldWrapper';
import Switch from '../Switch';
import CheckBox from '../CheckBox';
import FuseAutoComplete from '../AutoComplete/FuseAutoComplete';
import AsyncAutoComplete from '../AutoComplete/AsyncAutoComplete';
import AsyncAutoCompleteWithExtraParams from '../AutoComplete/AsyncAutoCompleteWithExtraParams';
import DropdownSelect from '../AutoComplete/DropdownSelect';
import Links from '../Links';
import CheckBoxGroup from '../CheckBoxGroup';
import RadioGroup from '../RadioGroup';
import AvatarAsyncAutoComplete from '../AvatarAsyncAutoComplete';
import MaskedInput from '../TextInput/MaskedInput';

const SkillPicker = dynamic(() => import('../SkillPicker'), { ssr: false });

const ImageCropperModal = dynamic(
  () => import('../ImageCropperModal/imageCropperModal.component'),
  {
    ssr: false,
  }
);

const AssignAccessibility = dynamic(() => import('../AssignAccessibility'), {
  ssr: false,
});

const MessageGroupCoverImage = dynamic(
  () => import('../MessageGroupCoverImage'),
  {
    ssr: false,
  }
);

const ImageCropper = dynamic(() => import('../ImageCropper'), {
  ssr: false,
});

const ImageDelete = dynamic(() => import('../ImageDelete'), {
  ssr: false,
});
const Datepicker = dynamic(() => import('../Datepicker'), {
  ssr: false,
});
const UserPicker = dynamic(() => import('../UserPicker'), {
  ssr: false,
});
const UsernameInput = dynamic(() => import('../UsernameInput'), {
  ssr: false,
});
const LanguagePicker = dynamic(() => import('../LanguagePicker'), {
  ssr: false,
});
const LatLanPicker = dynamic(() => import('../LatLanPicker'), {
  ssr: false,
});
const PhoneInput = dynamic(() => import('../PhoneInputSimple'), {
  ssr: false,
});

const RichText = dynamic(() => import('../RichText'), {
  ssr: false,
});
const JobWorkPlaceType = dynamic(() => import('../JobWorkPlaceType'), {
  ssr: false,
});
const JobQuestionPicker = dynamic(() => import('../JobQuestionPicker'), {
  ssr: false,
});
const HashtagPicker = dynamic(() => import('../HashtagPicker'), {
  ssr: false,
});
const ResumePicker = dynamic(() => import('../ResumePicker'), {
  ssr: false,
});

const LocationPicker = dynamic(() => import('../LocationPicker'), {
  ssr: false,
});

const CityPicker = dynamic(() => import('../CityPicker'), {
  ssr: false,
});
const JobQuestionAnswer = dynamic(() => import('../JobQuestionAnswer'), {
  ssr: false,
});
const AddExternalJobLink = dynamic(() => import('../AddExternalJobLink'), {
  ssr: false,
});
const SliderPicker = dynamic(() => import('../SliderPicker'), {
  ssr: false,
});
const RichTextEditor = dynamic(() => import('../RichTextEditor'), {
  ssr: false,
});
const AttachmentPicker = dynamic(() => import('../AttachmentPicker'), {
  ssr: false,
});

const SalaryPicker = dynamic(() => import('../SalaryPicker'), {
  ssr: false,
});
const ProjectCollaboratorSelector = dynamic(
  () => import('../ProjectCollaboratorSelector'),
  {
    ssr: false,
  }
);
const PriorityDropdown = dynamic(() => import('../PriorityDropdown'), {
  ssr: false,
});
const InputTags = dynamic(() => import('../InputTags'), {
  ssr: false,
});
const MenuItemWithSwitch = dynamic(() => import('../MenuItemWithSwitch'), {
  ssr: false,
});
const DayPicker = dynamic(() => import('../DayPicker'), {
  ssr: false,
});
const Pipelines = dynamic(() => import('../Pipelines'), {
  ssr: false,
});
const LinkGroup = dynamic(() => import('../LinkGroup'), {
  ssr: false,
});
const QuestionOptions = dynamic(() => import('../Questions/QuestionOptions'), {
  ssr: false,
});
const UserQuestion = dynamic(() => import('../QuestionItem'), {
  ssr: false,
});

const WorkAuthorization = dynamic(() => import('../WorkAuthorization'), {
  ssr: false,
});
const NumberInput = dynamic(() => import('../TextInput/NumberInput'), {
  ssr: false,
});
const CardBadge = dynamic(
  () => import('@shared/components/molecules/CardBadge'),
  {
    ssr: false,
  }
);

const components = {
  richTextEditor: { component: RichTextEditor },
  input: { component: TextInput },
  datePicker: { component: Datepicker },
  switch: { component: Switch },
  richtext: { component: RichText },
  checkBox: { component: CheckBox },
  fuseAutoComplete: { component: FuseAutoComplete },
  asyncAutoComplete: { component: AsyncAutoComplete },
  asyncAutoCompleteWithExtraParams: {
    component: AsyncAutoCompleteWithExtraParams,
  },
  avatarAsyncAutoComplete: { component: AvatarAsyncAutoComplete },
  projectCollaboratorSelector: {
    component: ProjectCollaboratorSelector,
  },
  links: { component: Links },
  imageCropper: { component: ImageCropper },
  imageDelete: { component: ImageDelete },
  userPicker: { component: UserPicker },
  dropdownSelect: { component: DropdownSelect },
  usernameInput: { component: UsernameInput },
  locationPicker: { component: LocationPicker },
  skillPicker: { component: SkillPicker },
  cityPicker: { component: CityPicker },
  languagePicker: { component: LanguagePicker },
  latLonPicker: { component: LatLanPicker },
  phoneInput: { component: PhoneInput },
  radioGroup: { component: RadioGroup },
  checkBoxGroup: { component: CheckBoxGroup },
  jobWorkPlaceType: { component: JobWorkPlaceType },
  jobQuestionPicker: { component: JobQuestionPicker },
  hashtagPicker: { component: HashtagPicker },
  resumePicker: { component: ResumePicker },
  jobQuestionAnswer: { component: JobQuestionAnswer },
  addExternalJobLink: { component: AddExternalJobLink },
  sliderPicker: { component: SliderPicker },
  attachmentPicker: { component: AttachmentPicker },
  salaryPicker: { component: SalaryPicker },
  imageCropperModal: { component: ImageCropperModal },
  messageGroupCoverImage: { component: MessageGroupCoverImage },
  assignAccessibility: { component: AssignAccessibility },
  priorityDropdown: { component: PriorityDropdown },
  inputTags: { component: InputTags },
  menuItemWithSwitch: { component: MenuItemWithSwitch },
  dayPicker: { component: DayPicker },
  pipelines: { component: Pipelines },
  linkGroup: { component: LinkGroup },
  questionOptions: { component: QuestionOptions },
  userQuestion: { component: UserQuestion },
  workAuthorization: { component: WorkAuthorization },
  numberInput: { component: NumberInput },
  cardBadge: { component: CardBadge },
  maskedInput: { component: MaskedInput },
};

export type ComponentNameTypes = keyof typeof components;

export type DynamicComponentBuilderItemProps<
  T extends ComponentNameTypes = ComponentNameTypes,
> = {
  cp: T | ReactNode;
  label?: string;
  name: string;
} & ComponentProps<(typeof components)[T]['component']>;

// props here are considered as any because some components imported has any-typed props. (like slider)
const ComponentBuilder = ({
  cp,
  label,
  name,
  ...rest
}: DynamicComponentBuilderItemProps) => {
  const component = isString(cp)
    ? components[cp as ComponentNameTypes].component
    : cp;

  if (!name) return React.createElement(component, { cp, ...rest });
  return (
    <FieldWrapper
      key={name}
      label={label}
      component={component}
      name={name}
      cp={cp}
      {...rest}
    />
  );
};

export default ComponentBuilder;
