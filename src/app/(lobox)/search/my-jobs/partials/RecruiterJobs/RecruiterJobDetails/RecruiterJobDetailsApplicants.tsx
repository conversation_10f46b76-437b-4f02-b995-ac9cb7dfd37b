import ApplicantCard from 'shared/components/molecules/ApplicantCard';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import SearchList from 'shared/components/Organism/SearchList';
import type {
  ApplicationProps,
  SingleJobAPIProps,
} from 'shared/types/jobsProps';
import { type FC } from 'react';
import useChangePipeline from 'shared/hooks/useChangePipeline';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { QueryKeys } from 'shared/utils/constants';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import { getJobApplications } from '@shared/utils/api/jobs';
import Button from '@shared/uikit/Button';
import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import classes from './RecruiterJobDetailsStyles.module.scss';

interface RecruiterJobDetailsApplicantsProps {
  job: SingleJobAPIProps;
}

const RecruiterJobDetailsApplicants: FC<RecruiterJobDetailsApplicantsProps> = ({
  job,
}) => {
  const { t } = useTranslation();
  const { onAction } = useRecruiterJobMoreActions();
  const {
    content,
    totalElements,
    totalPages,
    setPage,
    page,
    isLoading,
    refetch,
  } = usePaginateQuery<ApplicationProps>({
    action: {
      apiFunc: (props) =>
        getJobApplications(job.id, {
          ...props.params,
          size: 10,
        }),
      key: [QueryKeys.jobApplications, job.id],
    },
  });
  const { onChangePipeline } = useChangePipeline({
    onSuccess: refetch,
    variant: 'applicant',
  });
  const handleShareJob = () => onAction('share', job);

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={content}
      onPageChange={setPage}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      renderItem={(application) => (
        <ApplicantCard
          data={application}
          onChangePipeline={(pipeline) =>
            onChangePipeline({
              userId: application.id,
              pipelineId: pipeline.id as string,
            })
          }
          key={`application_${application.id}`}
        />
      )}
      emptyList={
        <EmptySearchResult
          sectionMessage={translateReplacer(t('no_antity_in_object'), [
            t('applicants').toLowerCase(),
            t('share_job').toLowerCase(),
          ])}
          className={classes.emptyResult}
          classNames={{ description: '!mt-12' }}
        >
          <Button
            label={t('share_job')}
            className="mt-20"
            leftIcon="share"
            leftType="far"
            onClick={handleShareJob}
          />
        </EmptySearchResult>
      }
      parentPage={page}
      noItemButtonAction
      innerList
    />
  );
};

export default RecruiterJobDetailsApplicants;
