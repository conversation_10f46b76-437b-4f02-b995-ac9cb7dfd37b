'use client';

import { hashtagNormalizer } from '@shared/utils/normalizers/hashtagNormalizer';
import { responseProjectsNormalizer } from '@shared/utils/normalizers/projects';
import { useMemo } from 'react';
import { searchFilterQueryParams } from 'shared/constants/search';
import useSearchQueries from 'shared/hooks/useSearchQueries';
import geoApi from 'shared/utils/api/geo';
import { db, Endpoints, jobsDb, searchEndPoints } from 'shared/utils/constants';
import { sortBy } from 'shared/utils/constants/enums/jobsDb';
import useTranslation from 'shared/utils/hooks/useTranslation';
import lookupResponseNormalizer, {
  lookupResponseNormalizerWithLabel,
} from 'shared/utils/normalizers/lookupResponseNormalizer';
import skillsResponseNormalizer from 'shared/utils/normalizers/skillsResponseNormalizer';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import type { CandidateSourceTypes } from '@shared/utils/constants/enums';
import {
  CANDIDATE_STATUS_VALUES,
  CANDIDATES_SOURCE_MAP,
} from '@shared/utils/constants/enums';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import hereApiResponseNormalizer from '@shared/utils/normalizers/hereApiResponseNormalizer';
import capitalize from 'lodash/capitalize';
import classes from './CandidateFilters.module.scss';

const sourceOptions = Object.values(CANDIDATES_SOURCE_MAP);

const toLabelValue = (item: string) => {
  const label = String(item).trim();
  return {
    value: label,
    label,
  };
};

export interface CandidateFiltersDataProps {
  searchId: string;
  languages: string[];
  occupations: string[];
  skills: string[];
  cities: { name: string; code: string }[];
  relatedPages: { id: string; title: string }[];
  jobs: {
    id: string;
    title: string;
    projects: CandidateFiltersDataProps['projects'];
  }[];
  salaryRange: {
    currencyCode: {};
    id: string;
    max: string;
    min: string;
    period: string;
  };
  projects: { id: string; title: string }[];
  tags: string[];
  clientIds: { id: string; title: string }[];
  vendorIds: { id: string; title: string }[];
  hashtags?: string[];
  travelRequirements?: string[];
}

export default function useCandidateSearch(
  filtersData?: CandidateFiltersDataProps
) {
  const { t } = useTranslation();
  const { handleChangeParams } = useCustomParams();
  const { authUser } = useGetAppObject();
  const countryCode = authUser?.location?.countryCode;
  const { getQueryValue } = useSearchQueries();

  const titlesOptions = useMemo(
    () => filtersData?.occupations?.map(toLabelValue) || [],
    [filtersData?.occupations]
  );
  const citiesData = useMemo(
    () =>
      filtersData?.cities?.map((city) => ({
        value: city.code,
        label: city.name,
      })) || [],
    [filtersData?.cities]
  );
  const skillsData = useMemo(
    () => filtersData?.skills?.map(toLabelValue) || [],
    [filtersData?.skills]
  );
  const languages = useMemo(
    () => filtersData?.languages?.map(toLabelValue) || [],
    [filtersData?.languages]
  );
  const projects = useMemo(
    () =>
      filtersData?.projects?.map((project) => ({
        value: project.id,
        label: project.title,
      })) || [],
    [filtersData?.projects]
  );
  const tags = useMemo(
    () => filtersData?.tags?.map(toLabelValue) || [],
    [filtersData?.tags]
  );
  const travelRequirements = useMemo(
    () =>
      filtersData?.travelRequirements?.map((item) => ({
        value: item,
        label: capitalize(item),
      })) || [],
    [filtersData?.travelRequirements]
  );
  const hashtags = useMemo(
    () => filtersData?.hashtags?.map(toLabelValue) || [],
    [filtersData?.hashtags]
  );
  const clientIds = useMemo(
    () =>
      filtersData?.clientIds?.map((project) => ({
        value: project.id,
        label: project.title,
      })) || [],
    [filtersData?.clientIds]
  );
  const relatedPages = useMemo(
    () =>
      filtersData?.relatedPages?.map((page) => ({
        value: page.id,
        label: page.title,
      })) || [],
    [filtersData?.relatedPages]
  );
  const vendorIds = useMemo(
    () =>
      filtersData?.vendorIds?.map((project) => ({
        value: project.id,
        label: project.title,
      })) || [],
    [filtersData?.vendorIds]
  );

  const sourceValue: CandidateSourceTypes =
    getQueryValue(searchFilterQueryParams.source) ?? 'ALL';

  const SOURCE = {
    name: searchFilterQueryParams.source,
    cp: 'list',
    options: sourceOptions,
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    hiddenInForm: true,
    label:
      sourceValue !== 'ALL' && sourceValue in CANDIDATES_SOURCE_MAP
        ? t(CANDIDATES_SOURCE_MAP[sourceValue].label)
        : t('source'),
    value: sourceValue,
    getValue: () => sourceValue,
    onChange: (value: {
      query: string;
      pathname: 'ALL' | 'LOBOX' | 'MANUAL';
    }) => {
      if (value.pathname === 'ALL') {
        handleChangeParams({
          remove: [searchFilterQueryParams.source],
        });
      } else {
        handleChangeParams({
          add: { [searchFilterQueryParams.source]: value.pathname },
        });
      }
    },
    divider: {
      className: classes.groupDivider,
    },
  };
  const STATUS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('status'),
    },
    label: t('status'),
    cp: 'radioGroup',
    name: searchFilterQueryParams.status,
    divider: {
      className: classes.groupDivider,
    },
    options: [
      CANDIDATE_STATUS_VALUES.YES,
      CANDIDATE_STATUS_VALUES.NO,
      CANDIDATE_STATUS_VALUES.PREFER_NOT_TO_SAY,
    ],
    getValue: () => getQueryValue(searchFilterQueryParams.status) || 'ALL',
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    schema: 'semi-transparent',
    isDefaultValue:
      !getQueryValue(searchFilterQueryParams.status) ||
      getQueryValue(searchFilterQueryParams.status) === 'ALL',
  };
  const TITLES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('j_title'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.occupations,
    options: titlesOptions,
    asyncAutoCompleteProps: {
      maxLength: 100,
      // apiFunc:
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizerWithLabel,
    },
    label: t('j_title'),
    placeholder: t('search_j_title'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.occupations, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: citiesData,
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    alwaysShowInHeader: true,
    asyncAutoCompleteProps: {
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  // side filter
  const SORT_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('sort_by'),
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.sortBy,
    options: sortBy,
    label: t('sort_by'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.sortBy) || 'MOST_RELEVANT',
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const SKILLS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('skills'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    name: searchFilterQueryParams.skills,
    options: skillsData,
    label: t('skills'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getSkills,
      normalizer: skillsResponseNormalizer,
      plusButtonClassName: classes.plusButtonClassName,
    },
    placeholder: t('search_skill'),
    getValue: () => getQueryValue(searchFilterQueryParams.skills, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: classes.header,
    },
    name: searchFilterQueryParams.languages,
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    label: t('languages'),
    placeholder: t('search_language'),
    options: languages,
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    getValue: () => getQueryValue(searchFilterQueryParams.languages, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const PROJECTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('projects'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.projectIds,
    options: projects,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('projects'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.projectIds, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const JOBS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('jobs'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.jobIds,
    options: projects,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('jobs'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.jobIds, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const VENDOR = {
    formGroup: {
      color: 'smoke_coal',
      title: t('vendor'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.vendorIds,
    options: vendorIds,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('vendor'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.vendorIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const CLIENT = {
    formGroup: {
      color: 'smoke_coal',
      title: t('client'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.clientIds,
    options: clientIds,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('client'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.clientIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const TAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('tags'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.tags,
    options: tags,
    label: t('tags'),
    getValue: () => getQueryValue(searchFilterQueryParams.tags, 'array'),
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const TRAVEL_REQUIREMENTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('willing_to_travel'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.travelRequirements,
    options: travelRequirements,
    label: t('willing_to_travel'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.travelRequirements, 'array'),
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: hashtags,
    label: t('hashtags'),
    placeholder: t('search_hashtag'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: searchEndPoints.suggestHashtag,
      normalizer: hashtagNormalizer,
    },
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };

  const PAGES = {
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    divider: {
      className: classes.groupDivider,
    },
    placeholder: t('search_company'),
    asyncAutoCompleteProps: {
      plusButtonClassName: classes.plusButtonClassName,
      maxLength: 100,
      url: `${Endpoints.App.Common.suggestPlace}`,
      normalizer: hereApiResponseNormalizer,
    },
    hiddenInBusiness: true,

    formGroup: {
      color: 'smoke_coal',
      title: t('related_with'),
      className: classes.header,
    },
    label: t('related_with'),
    hiddenInHeader: false,
    alwaysShowInHeader: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.relatedPageIds, 'array'),
    name: searchFilterQueryParams.relatedPageIds,
    options: relatedPages,
  };

  const OPEN_TO_WORK = {
    name: 'openToWork',
    cp: 'dropdownSelect',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    formGroup: {
      color: 'smoke_coal',
      title: t('candidate_status'),
      className: classes.header,
    },
    label: t('candidate_status'),
    required: true,
    options: Object.values(CANDIDATE_STATUS_VALUES),
  };

  const SALARY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('salary_range'),
      className: '!py-0 !mb-12',
    },
    cp: 'salaryPicker',
    withConfirmation: false,
    name: searchFilterQueryParams.salaryRange,
    data: filtersData?.salaryRange ?? {},
    getValue: () => getQueryValue(searchFilterQueryParams.salaryRange),
    label: t('salary_range'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };

  const savedFiltersGroups = [
    {
      name: 'text',
      cp: 'input',
      label: t('searched_txt'),
      helperText: t('this_is_tsym_b_tsf'),
      wrapStyle: 'col-span-full',
      visibleOptionalLabel: false,
    },
    {
      ...STATUS,
      options: Object.values(CANDIDATES_SOURCE_MAP),
      name: 'openToWork',
      formGroup: {
        color: 'smoke_coal',
        title: t('source'),
      },
    },
    {
      ...PROJECTS,
      formGroup: {
        color: 'smoke_coal',
        title: t('linked_projects'),
      },
      getFormValue: (val) => {
        console.log({ val });
        return val?.map((item) => item.value);
      },
    },
    {
      formGroup: {
        title: t('exp_level'),
        color: 'smoke_coal',
      },
      name: 'experienceLevel',
      cp: 'checkBoxGroup',
      options: jobsDb.experienceLevels,
    },
    {
      formGroup: {
        color: 'smoke_coal',
        title: t('status'),
      },
      cp: 'radioGroup',
      name: searchFilterQueryParams.source,
      options: [
        CANDIDATES_SOURCE_MAP.ALL,
        CANDIDATE_STATUS_VALUES.YES,
        CANDIDATE_STATUS_VALUES.NO,
      ],
    },
    SORT_BY,
    {
      ...JOBS,
      formGroup: {
        color: 'smoke_coal',
        title: t('job_title'),
      },
    },
    {
      formGroup: {
        title: t('job_type_pref'),
        color: 'smoke_coal',
      },
      cp: 'checkBoxGroup',
      name: 'employmentType',
      options: db.EMPLOYMENT_TYPES,
    },
    {
      formGroup: {
        color: 'smoke_coal',
        title: t('member_since'),
        className: classes.header,
      },
      cp: 'radioGroup',
      name: searchFilterQueryParams.memberSince,
      divider: {
        className: classes.groupDivider,
      },
      options: jobsDb.memberSince,
    },
    TITLES,
    SKILLS,
    LANGUAGES,
    {
      ...LOCATION,
      getFormValue: (val) => {
        console.log({ val });
        return val?.map((item) => ({
          code: item.value,
          name: item.label,
        }));
      },
    },
    PAGES,
    TRAVEL_REQUIREMENTS,
    TAGS,
    CLIENT,
    VENDOR,
  ];
  const groups = [
    SOURCE,
    TITLES,
    SKILLS,
    LANGUAGES,
    LOCATION,
    PROJECTS,
    CLIENT,
    VENDOR,
    JOBS,
    STATUS,
    SORT_BY,
    SALARY,
    TAGS,
    HASHTAGS,
    TRAVEL_REQUIREMENTS,
  ];

  return { groups, savedFiltersGroups };
}
